<template>
  <section>
    <!-- （第7条线）出料加工进口薄片-分析单表头 -->
    <a-card size="small" title="表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">



           <!-- 客户 -->
          <a-form-item name="customerCode"   :label="'客户'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable  || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customerCode" id="customerCode">
              <a-select-option class="cs-select-dropdown" v-for="item in customerList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

            <!-- 收货人 -->
            <a-form-item name="receiver"   :label="'收货人'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable  || props.isAllConfirmed"  size="small" allow-clear v-model:value="formData.receiver" />
            </a-form-item>

            <!-- 合同号 弹框选择不允许修改 -->
            <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
                <a-input :disabled="true"  size="small" v-model:value="formData.contractNo" />
            </a-form-item>

            <!-- 分析单号  唯一性检验 -->
            <a-form-item name="analysisNo"   :label="'分析单号'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable  || props.isAllConfirmed"  size="small" v-model:value="formData.analysisNo" />
            </a-form-item>


            <!-- 贸易国别 -->
            <a-form-item name="tradeCountry"   :label="'贸易国别'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable  || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.tradeCountry" id="tradeCountry">
                <a-select-option class="cs-select-dropdown" v-for="item in countryList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>

            <!-- 目的地 -->
            <a-form-item name="destination"   :label="'目的地'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable  || props.isAllConfirmed"  size="small" v-model:value="formData.destination" />
            </a-form-item>

            <!-- 币种 -->
            <a-form-item name="curr"   :label="'币种'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable  || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.curr" id="curr">
                <a-select-option class="cs-select-dropdown" v-for="item in currList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>

            <!-- 消费国别 -->
            <a-form-item name="consumeCountry"   :label="'消费国别'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable  || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.consumeCountry" id="tradeCountry">
                <a-select-option class="cs-select-dropdown" v-for="item in countryList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>

            <!-- 是否转运 -->
            <a-form-item name="isTransit"   :label="'是否转运'" class="grid-item"  :colon="false">
              <cs-select :disabled="showDisable  || props.isAllConfirmed"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.isTransit" id="tradeCountry">
                <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isTransitList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                  {{item.value}} {{item.label }}
                </a-select-option>
              </cs-select>
            </a-form-item>

            <!-- 装运期限 -->
            <a-form-item name="shipmentDeadline"   :label="'装运期限'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled="showDisable   || props.isAllConfirmed"
                v-model:value="formData.shipmentDeadline"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>

            <!-- shipper -->
            <a-form-item name="shipper"   :label="'SHIPPER'" class="grid-item merge-3"   :colon="false">
              <a-textarea
                :disabled="showDisable  || props.isAllConfirmed"
                size="small"
                v-model:value="formData.shipper"
                :autoSize="{ minRows: 2, maxRows: 3 }"
                allow-clear>
              </a-textarea>
            </a-form-item>

            <!-- consignee -->
            <a-form-item name="consignee"   :label="'CONSIGNEE'"  class="grid-item merge-3"  :colon="false">
              <a-textarea
                :disabled=" showDisable || props.isAllConfirmed"
                size="small"
                v-model:value="formData.consignee"
                :autoSize="{ minRows: 2, maxRows: 3 }"
                allow-clear>
              </a-textarea>
            </a-form-item>

            <!-- notify party -->
            <a-form-item name="notifyParty"   :label="'NOTIFY PARTY'" class="grid-item merge-3"  :colon="false">
              <a-textarea
                :disabled=" showDisable || props.isAllConfirmed"
                size="small"
                v-model:value="formData.notifyParty"
                :autoSize="{ minRows: 2, maxRows: 3 }"
                allow-clear>
              </a-textarea>
            </a-form-item>

            <!-- freight -->
            <a-form-item name="freight"   :label="'FREIGHT'" class="grid-item merge-3"  :colon="false">
              <a-textarea
                :disabled=" showDisable || props.isAllConfirmed"
                size="small"
                v-model:value="formData.freight"
                :autoSize="{ minRows: 2, maxRows: 3 }"
                allow-clear>
              </a-textarea>
            </a-form-item>

            <!-- 出境加工账册编号 -->
            <a-form-item name="processAccountNo"   :label="'出境加工账册编号'" class="grid-item"  :colon="false">
                <a-input :disabled=" showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.processAccountNo" />
            </a-form-item>

            <!-- 装箱时间 -->
            <a-form-item name="packingTime"   :label="'装箱时间'" class="grid-item"  :colon="false">
              <a-date-picker
                allow-clear
                :disabled=" showDisable || props.isAllConfirmed "
                v-model:value="formData.packingTime"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>

            <!-- 至 -->
            <a-form-item name="packingTimeTo"   :label="'至'" class="grid-item "  :colon="false">
              <a-date-picker
                allow-clear
                :disabled=" showDisable || props.isAllConfirmed "
                v-model:value="formData.packingTimeTo"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>

            <!-- 仓库地址 -->
            <a-form-item name="warehouseAddress"   :label="'仓库地址'" class="grid-item  merge-3"  :colon="false">
              <a-textarea
                :disabled=" showDisable || props.isAllConfirmed"
                size="small"
                v-model:value="formData.warehouseAddress"
                :autoSize="{ minRows: 2, maxRows: 3 }"
                allow-clear>
              </a-textarea>
            </a-form-item>

            <!-- 船名航次 -->
            <a-form-item name="shipNameVoyage"   :label="'船名航次'" class="grid-item"  :colon="false">
                <a-input :disabled=" showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.shipNameVoyage" />
            </a-form-item>

            <!-- 提单编号 -->
            <a-form-item name="billNo"   :label="'提单编号'" class="grid-item"  :colon="false">
                <a-input :disabled=" showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.billNo" />
            </a-form-item>

            <!-- 联系人及电话 -->
            <a-form-item name="contactPhone"   :label="'联系人及电话'" class="grid-item"  :colon="false">
                <a-input :disabled=" showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.contactPhone" />
            </a-form-item>

            <!-- 提单日期 -->
            <a-form-item name="billDate"   :label="'提单日期'" class="grid-item"  :colon="false">
              <a-date-picker
                :disabled=" showDisable || props.isAllConfirmed"
                v-model:value="formData.billDate"
                id="releaseDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""></a-date-picker>
            </a-form-item>


          <!-- 制单人 -->
          <a-form-item name="createBy"   :label="'制单人'" class="grid-item"  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.createBy" allow-clear />
          </a-form-item>

          <!-- 制单日期 -->
          <a-form-item name="createTime"   :label="'制单日期'" class="grid-item"  :colon="false">
            <a-date-picker
              allow-clear
              :disabled="true"
              v-model:value="formData.createTime"
              id="releaseDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>





          <!-- 单据状态 -->
          <a-form-item name="dataState"   :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.dataState" id="dataState">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.orderStatus"  :key="item.value +' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 确认时间 -->
          <a-form-item name="confirmTime"   :label="'确认时间'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="releaseDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>


          <!-- 审批状态 -->
<!--          <a-form-item name="approvalStatus"   :label="'审批状态'" class="grid-item"  :colon="false">-->
<!--            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.approvalStatus" id="approvalStatus">-->
<!--              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.approval_status"  :key="item.value +' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
<!--          </a-form-item>-->


          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      :loading="buttonLoadingMap.save"
                      :disabled=" props.isAllConfirmed "
                      v-show="props.editConfig.editStatus !==  editStatus.SHOW">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="handlerOnBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


    <a-card size="small" title="出料加工列表" class="cs-card-form"   v-if="editConfig">
      <biz-bp-analyse-order-list :head-id="props.editConfig.editData.id" :is-all-confirmed="props.isAllConfirmed" :showDisable="showDisable" ref="bpAnalyseOrderListRef" />
    </a-card>



    <a-card size="small" title="集装箱列表" class="cs-card-form"   v-if="editConfig">
      <biz-bp-analyse-order-list-box-list :head-id="props.editConfig.editData.id" :is-all-confirmed="props.isAllConfirmed" :showDisable="showDisable" ref="bpAnalyseOrderBoxRef" />
    </a-card>


  </section>
</template>

<script setup>
  import {editStatus, productClassify} from '@/view/common/constant'
  import {message} from "ant-design-vue";
  import {onMounted, reactive, ref} from "vue";
  import CsSelect from "@/components/select/CsSelect.vue";
  import {usePCode} from "@/view/common/usePCode";
  import ycCsApi from "@/api/ycCsApi";
  import {useButtonLoading} from "@/view/utils/useBtnLoading";
  import BizBpAnalyseOrderList from "@/view/dec/bp/list/BizBpAnalyseOrderListList.vue";
  import BizBpAnalyseOrderListBoxList from "@/view/dec/bp/box/BizBpAnalyseOrderListBoxList.vue";
  import BizBpAddBoxInfo from "@/view/dec/bp/componment/BizBpAddBoxInfo.vue";
  const { getPCode } = usePCode()
  const { setLoading,buttonLoadingMap } = useButtonLoading()


  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => {
      }
    },
    isAllConfirmed: {
      type: Boolean,
      default: false
    }
  })



  // 定义子组件 emit事件，用于子组件向父组件传递数据
  const emit = defineEmits(['onEditBack']);

  const handlerOnBack = (val) => {
    emit('onEditBack', val);
  };

  /**
   * 校验分析单号是否唯一
   * @param rule 校验规则
   * @param value 校验值
   * @param callback 回调函数
   */
  const checkAnalyseOrderCode = async (rule, value) => {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await window.majesty.httpUtil.postAction(
          ycCsApi.bizBpAnalyseOrderHead.checkAnalyseOrderCode,
          formData
        );

        if (res.code === 200) {
          if (res && res.data && res.data === 999) {
            reject(res.message);
          } else if (res.data > 0) {
            reject('分析单号已经存在！');
          } else {
            resolve(); // 校验通过
          }
        } else {
          resolve(); // 或根据业务逻辑决定是否 reject
        }
      } catch (error) {
        console.error("校验请求失败:", error);
        reject("网络异常，请重试");
      }
    });
  };

  // 是否禁用
  const showDisable = ref(false)

  // 表单数据
  const formData = reactive({
    // 主键id
    id:'',
    // 业务类型
    businessType:'',
    // 数据状态
    dataState:'',
    // 版本号
    versionNo:'',
    // 企业10位编码
    tradeCode:'',
    // 组织机构代码
    sysOrgCode:'',
    // 父级id
    parentId:'',
    // 创建人
    createBy:'',
    // 创建时间
    createTime:'',
    // 更新人
    updateBy:'',
    // 更新时间
    updateTime:'',
    // 插入用户名
    insertUserName:'',
    // 更新用户名
    updateUserName:'',
    // 扩展字段1
    extend1:'',
    // 扩展字段2
    extend2:'',
    // 扩展字段3
    extend3:'',
    // 扩展字段4
    extend4:'',
    // 扩展字段5
    extend5:'',
    // 扩展字段6
    extend6:'',
    // 扩展字段7
    extend7:'',
    // 扩展字段8
    extend8:'',
    // 扩展字段9
    extend9:'',
    // 扩展字段10
    extend10:'',
    // 收货人
    receiver:'',
    // 合同号
    contractNo:'',
    // 分析单号
    analysisNo:'',
    // 贸易国别
    tradeCountry:'',
    // 目的地
    destination:'',
    // 消费国别
    consumeCountry:'',
    // 是否转运
    isTransit:'',
    // 装运期限
    shipmentDeadline:'',
    // shipper
    shipper:'',
    // consignee
    consignee:'',
    // notify party
    notifyParty:'',
    // freight
    freight:'',
    // 出境加工账册编号
    processAccountNo:'',
    // 装箱时间
    packingTime:'',
    // 至
    packingTimeTo:'',
    // 仓库地址
    warehouseAddress:'',
    // 船名航次
    shipNameVoyage:'',
    // 提单编号
    billNo:'',
    // 联系人及电话
    contactPhone:'',
    // 提单日期
    billDate:'',
    // 审核状态
    apprStatus:'',
    // 币制
    curr:'',
    // 确认时间
    confirmTime:'',
    // 表体金额汇总
    total:'',
    // 客户
    customerCode:''
  })
  // 校验规则
  const rules = {
      id:[
          {max: 80, message: '主键id长度不能超过 80位字节', trigger: 'blur'}
      ],
      businessType:[
          {max: 120, message: '业务类型长度不能超过 120位字节', trigger: 'blur'}
      ],
      dataState:[
          {max: 20, message: '单据状态长度不能超过 20位字节', trigger: 'blur'},
          {required: true, message: '单据状态不能为空', trigger: 'blur'}
      ],
      versionNo:[
          {max: 20, message: '版本号长度不能超过 20位字节', trigger: 'blur'}
      ],
      tradeCode:[
          {max: 20, message: '企业10位编码长度不能超过 20位字节', trigger: 'blur'}
      ],
      sysOrgCode:[
          {max: 20, message: '组织机构代码长度不能超过 20位字节', trigger: 'blur'}
      ],
      parentId:[
          {max: 80, message: '父级id长度不能超过 80位字节', trigger: 'blur'}
      ],
      createBy:[
          {required: true, message: '制单人不能为空', trigger: 'blur'},
          {max: 100, message: '制单人长度不能超过 100位字节', trigger: 'blur'}
      ],
      createTime:[
        {required: true, message: '制单事件不能为空', trigger: 'blur'},
      ],
      updateBy:[
          {max: 100, message: '更新人长度不能超过 100位字节', trigger: 'blur'}
      ],
      updateTime:[
      ],
      insertUserName:[
          {max: 100, message: '插入用户名长度不能超过 100位字节', trigger: 'blur'}
      ],
      updateUserName:[
          {max: 100, message: '更新用户名长度不能超过 100位字节', trigger: 'blur'}
      ],
      extend1:[
          {max: 400, message: '扩展字段1长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend2:[
          {max: 400, message: '扩展字段2长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend3:[
          {max: 400, message: '扩展字段3长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend4:[
          {max: 400, message: '扩展字段4长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend5:[
          {max: 400, message: '扩展字段5长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend6:[
          {max: 400, message: '扩展字段6长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend7:[
          {max: 400, message: '扩展字段7长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend8:[
          {max: 400, message: '扩展字段8长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend9:[
          {max: 400, message: '扩展字段9长度不能超过 400位字节', trigger: 'blur'}
      ],
      extend10:[
          {max: 400, message: '扩展字段10长度不能超过 400位字节', trigger: 'blur'}
      ],
      receiver:[
          {max: 1000, message: '收货人长度不能超过 1000位字节', trigger: 'blur'}
      ],
      contractNo:[
          {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'},
          {required:true,message: '合同号不能为空',trigger: 'blur'}
      ],
      analysisNo:[
          {max: 60, message: '分析单号长度不能超过 60位字节', trigger: 'blur'},
          {required:true,validator:checkAnalyseOrderCode,trigger: 'blur'  },
          {required:true,message: '分析单号不能为空',trigger: 'blur'}
      ],
      tradeCountry:[
          {max: 50, message: '贸易国别长度不能超过 50位字节', trigger: 'blur'}
      ],
      destination:[
          {max: 100, message: '目的地长度不能超过 100位字节', trigger: 'blur'}
      ],
      consumeCountry:[
          {max: 50, message: '消费国别长度不能超过 50位字节', trigger: 'blur'}
      ],
      isTransit:[
          {max: 10, message: '是否转运长度不能超过 10位字节', trigger: 'blur'}
      ],
      shipmentDeadline:[
      ],
      shipper:[
          {max: 500, message: 'shipper长度不能超过 500位字节', trigger: 'blur'}
      ],
      consignee:[
          {max: 500, message: 'consignee长度不能超过 500位字节', trigger: 'blur'}
      ],
      notifyParty:[
          {max: 500, message: 'notify party长度不能超过 500位字节', trigger: 'blur'}
      ],
      freight:[
          {max: 500, message: 'freight长度不能超过 500位字节', trigger: 'blur'}
      ],
      processAccountNo:[
          {max: 50, message: '出境加工账册编号长度不能超过 50位字节', trigger: 'blur'}
      ],
      packingTime:[
      ],
      packingTimeTo:[
      ],
      warehouseAddress:[
          {max: 500, message: '仓库地址长度不能超过 500位字节', trigger: 'blur'}
      ],
      shipNameVoyage:[
          {max: 100, message: '船名航次长度不能超过 100位字节', trigger: 'blur'}
      ],
      billNo:[
          {max: 100, message: '提单编号长度不能超过 100位字节', trigger: 'blur'}
      ],
      contactPhone:[
          {max: 100, message: '联系人及电话长度不能超过 100位字节', trigger: 'blur'}
      ],
      billDate:[
      ],
      curr:[
        {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
      ],
      customerName:[
        {max: 200, message: '客户长度不能超过 200位字节', trigger: 'blur'}
      ]
  }





  const formRef = ref(null);
  // 保存
  const handlerSave = async () => {
    try {
      await formRef.value.validate()
      setLoading("save", true)
      const res = await window.majesty.httpUtil.putAction(`${ycCsApi.bizBpAnalyseOrderHead.update}/${formData.id}`, formData)
      if (res.code !== 200) {
        message.error(res.message)
        return
      }else {
        Object.assign(formData, res.data)
        message.success("保存成功")
      }
    } catch (error) {
      console.error("保存失败:", error)
    } finally {
      setLoading("save", false)
    }
  }



  // ———————————————————————————————————————— 获取常用下拉参数 ————————————————————————————————————————
  // private List<Map<String,String>> unitList;
  // private List<Map<String,String>> currList;
  // private List<Map<String,String>> customerList;
  // private List<Map<String,String>> countryList;
  const pCode = ref('')
  const countryList = ref(null);
  const currList = ref(null);
  const initKeyValueParams = async () => {
    const res = await  window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderHead.getCommonKeyValueList,{})
    if (res.code === 200) {
      countryList.value = res.data.countryList
      currList.value = res.data.currList
      customerList.value = res.data.customerList
    }else {
      console.log('No Search Country List!',res)
    }
  }

  // 表体ref
  const bpAnalyseOrderListRef = ref(null)
  // 表体boxList
  const bpAnalyseOrderBoxRef = ref(null)

  // 客户列表
  const customerList = ref([])

  // 初始化操作
  onMounted(() => {
    // 初始化键值对参数
    initKeyValueParams()
    getPCode().then(res=>{
      console.log('res',res)
      pCode.value = res;
    })
    if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
      showDisable.value = false
      Object.assign(formData, {});
    }
    // 初始化数据
    if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      Object.assign(formData, props.editConfig.editData);
      showDisable.value = false
    }
    if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
      Object.assign(formData, props.editConfig.editData);
      showDisable.value = true
    }
  });






</script>

<style lang="less" scoped>


</style>



