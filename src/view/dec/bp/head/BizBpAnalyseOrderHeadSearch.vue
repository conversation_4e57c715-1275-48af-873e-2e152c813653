<template>
  <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form grid-container" >



    <!--  数据状态  -->
    <a-form-item name="dataState"  label="单据状态" class="grid-item"  :colon="false">
      <cs-select
        :options="productClassify.orderStatus"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.dataState"
        id="dataState"
      />
    </a-form-item>


    <!--  合同号  -->
    <a-form-item name="contractNo"  label="合同号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>



    <!--  分析单号  -->
    <a-form-item name="analysisNo"  label="分析单号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.analysisNo" />
    </a-form-item>

    <!--  客户  -->
    <a-form-item name="customerCode"  label="客户" class="grid-item"  :colon="false">
      <cs-select
        :options="customerCodeList"
        :combine-display="true"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.customerCode"
        id="customerCode"
      />
    </a-form-item>




    <!--  创建时间   -->
    <a-form-item name="createTime" label="制单日期"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeFrom"
              id="createTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeTo"
              id="createTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>




    <!--  装运期限   -->
    <a-form-item name="shipmentDeadline" label="装运期限"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.shipmentDeadlineFrom"
              id="shipmentDeadlineFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.shipmentDeadlineTo"
              id="shipmentDeadlineTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>




    <!-- 制单人 -->
    <a-form-item name="createBy"  label="制单人" class="grid-item"  :colon="false">
      <cs-select
        :options="createByList"
        :combine-display="false"
        option-filter-prop="label"
        option-label-prop="key"
        allow-clear
        show-search
        v-model:value="searchParam.createBy"
        id="createBy"
      />

    </a-form-item>







  </a-form>
</template>

<script setup>
import {onMounted, reactive, ref} from 'vue'
  import CsSelect from "@/components/select/CsSelect.vue";
  import {productClassify} from "@/view/common/constant";
import ycCsApi from "@/api/ycCsApi";

  defineOptions({
    name: 'BizBpAnalyseOrderHeadSearch',
  })

  /* 定义重置方法(注意前后顺序) */
  const resetSearch = () => {
    Object.keys(searchParam).forEach(key => {
      searchParam[key] = '';
    });
  }

  const searchParam = reactive({
    id:'',
    businessType:'',
    dataState:'',
    versionNo:'',
    tradeCode:'',
    sysOrgCode:'',
    parentId:'',
    createBy:'',
    createTime:'',
    createTimeTo:'',
    createTimeFrom:'',
    updateBy:'',
    updateTime:'',
    updateTimeTo:'',
    updateTimeForm:'',
    insertUserName:'',
    updateUserName:'',
    extend1:'',
    extend2:'',
    extend3:'',
    extend4:'',
    extend5:'',
    extend6:'',
    extend7:'',
    extend8:'',
    extend9:'',
    extend10:'',
    receiver:'',
    contractNo:'',
    analysisNo:'',
    tradeCountry:'',
    destination:'',
    consumeCountry:'',
    isTransit:'',
    shipmentDeadline:'',
    shipmentDeadlineTo:'',
    shipmentDeadlineFrom:'',
    shipper:'',
    consignee:'',
    notifyParty:'',
    freight:'',
    processAccountNo:'',
    packingTime:'',
    packingTimeTo:'',
    packingTimeForm:'',
    packingTimeToTo:'',
    packingTimeToForm:'',
    warehouseAddress:'',
    shipNameVoyage:'',
    billNo:'',
    contactPhone:'',
    billDate:'',
    billDateTo:'',
    billDateForm:'',
    apprStatus:'',
    curr:'',
    confirmTime:'',
    confirmTimeTo:'',
    confirmTimeForm:'',
    total:'',
    customerCode:'',
  })


  defineExpose({searchParam, resetSearch});


  // 客户列表
  const customerCodeList = ref([]);
  const createByList = ref([]);

  // 初始化查询下拉参数
  const getSearchKeyValueList = async () => {
    const res  = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderHead.getCommonSearchList,{})
    if (res.code === 200) {
      customerCodeList.value = res.data.customerList;
      createByList.value = res.data.createByList;
    }else {
      console.log('获取客户信息失败')
    }
  }



  onMounted(() => {
    getSearchKeyValueList();
  });
</script>

<style lang='less' scoped>

</style>
