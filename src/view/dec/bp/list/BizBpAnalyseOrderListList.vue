 <template>
  <!-- （第7条线）出料加工进口薄片-分析单表表体 -->
  <section  class="dc-section">
    <div class="cs-action" >

      <!-- 操作按钮区域 -->
      <!--
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:add']">
            <a-button size="small" @click="handlerAdd" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
      </div>
      -->
      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>

          <template #bodyCell="{text, record, index, column, key }">
            <!-- 净重 -->
            <template v-if="column.dataIndex === 'netWt'">
              <a-input-number
                v-if="props.showDisable === false || props.isAllConfirmed === false"
                size="small"
                v-model:value="dataSourceList[index].netWt"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleTableUpdate(record,column);
                }"
                @keydown.enter="() => {
                  handleTableUpdate(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 体积 -->
            <template v-if="column.dataIndex === 'volume'">
              <a-input-number
                v-if="props.showDisable === false || props.isAllConfirmed === false"
                size="small"
                v-model:value="dataSourceList[index].volume"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleTableUpdate(record,column);
                }"
                @keydown.enter="() => {
                  handleTableUpdate(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>



  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import { useCommon } from '@/view/common/useCommon'
  import { createVNode, onMounted, provide, reactive, ref} from "vue";
  import { message, Modal } from "ant-design-vue";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import { deleteClient } from "@/api/bi/bi_client_info";
  import ycCsApi from "@/api/ycCsApi";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  import {isNullOrEmpty} from "@/view/utils/common";
  import {getInComingListBySid} from "@/api/cs_api_constant";
  const { inputFormatter, inputParser,formatNumber,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()

  /* 引入通用方法 */
  const {
    page,
    dataSourceList,
    tableLoading

  } = useCommon()


  const props = defineProps({
    headId:{
      type:String,
      default:()=>'',
      required:true
    },
    isAllConfirmed:{
      type:Boolean,
      default:()=>false
    },
    showDisable:{
      type:Boolean,
      default:()=>false
    }
  })



  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '产品型号',
      width: 200,
      align: 'center',
      dataIndex: 'productModel',
      key: 'productModel',
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '箱数',
      width: 200,
      align: 'center',
      dataIndex: 'boxCount',
      key: 'boxCount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '毛重',
      width: 200,
      align: 'center',
      dataIndex: 'grossWeight',
      key: 'grossWeight',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '净重',
      width: 200,
      align: 'center',
      dataIndex: 'netWt',
      key: 'netWt',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '体积',
      width: 200,
      align: 'center',
      dataIndex: 'volume',
      key: 'volume',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '备注',
      width: 200,
      align: 'center',
      dataIndex: 'remark',
      key: 'remark'
    }

  ])


  defineOptions({
    name: 'BizBpAnalyseOrderList',
  });


  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 查询数据 */
  const getList = async () => {
    tableLoading.value = true
    let params =  {
      parentId : props.headId
    }
    try {
      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizBpAnalyseOrderList.list}?page=${page.current}&limit=${page.pageSize}`, params);
      dataSourceList.value = res.data
      page.total = res.total
      // 重置选择数据
      // restSelectData()
    }catch (error) {
      console.log(error)
    }finally {
      tableLoading.value = false
    }
  }



  // 处理更新表单数据
  const handleUpdateTable = async (record) => {
    const tempData = record

    tableLoading.value = true
    try {
      await window.majesty.httpUtil.postAction(`${ycCsApi.bizBpAnalyseOrderList.update}`, record);
      if (res.code === 200) {
        message.success('更新成功')
      }else {

        message.error(res.message)
      }
      message.success('更新成功');
    } catch (error) {
      console.log(error)
    }finally {
      tableLoading.value = false
    }

  }



  const isEditLoading = ref(false)
  const handleTableUpdate = async (record, column) => {
    let dataTemp = null;
    if (isEditLoading.value === true) {
      console.log('回车，失焦同时触发！');
      return;
    }

    isEditLoading.value = true;

    if (!record || !record.id) {
      isEditLoading.value = false;
      return;
    }

    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizBpAnalyseOrderList.getListById,record);
      if (res.code !== 200) {
        isEditLoading.value = false;
        return;
      }

      dataTemp = res.data;

      if (column.dataIndex === 'netWt') {
        if (isNullOrEmpty(dataTemp) || dataTemp.netWt === record.netWt) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.putAction(`${ycCsApi.bizBpAnalyseOrderList.update}/${record.id}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
        } else {
          record.netWt = dataTemp.netWt; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }else if (column.dataIndex === 'volume') {
        if (isNullOrEmpty(dataTemp) || dataTemp.volume === record.volume) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.putAction(`${ycCsApi.bizBpAnalyseOrderList.update}/${record.id}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
        } else {
          record.volume = dataTemp.volume; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


    } catch (error) {
      record.netWt = dataTemp?.netWt; // 出错恢复原值
      record.volume = dataTemp?.volume; // 出错恢复原值
      message.error(error.message);
      console.error("数量变更异常：", error);
    } finally {
      setTimeout(() => {
        isEditLoading.value = false;
      }, 100);
    }
  };











  onMounted(()=> {

    getList()

  })

</script>

<style lang="less" scoped>


</style>
