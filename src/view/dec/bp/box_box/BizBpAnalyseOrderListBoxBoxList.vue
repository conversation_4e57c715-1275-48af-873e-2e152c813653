<template>
  <!-- （第7条线）出料加工进口薄片-装箱列表-子表的子表 -->
  <section class="dc-section">
    <div class="cs-action">
      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"

          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 暂无数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <export_sell_list-head-edit :editConfig="editConfig" @on-back="handlerOnBack"/>
    </div>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig" @onImportSuccess="importSuccess"></ImportIndex>


  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, h, onMounted, reactive, ref} from "vue";
  import ycCsApi from "@/api/ycCsApi";
  import {usePCode} from "@/view/common/usePCode";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  const { getPCode } = usePCode()
  const { cmbShowRender } = useColumnsRender()
  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    handleEditByRow,
    handleViewByRow,
    operationEdit,
    onPageChange,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    ajaxUrl,
    doExport,
    handlerRefresh

  } = useCommon()


  defineOptions({
    name: 'BizBpAnalyseOrderListBoxBoxList',
  });


  const props = defineProps({
    headId: {
      type: String,
      default: '',
      required: true
    }
  })


  const importShow = ref(false)

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '集装箱规格',
      minWidth: 200,
      align: 'center',
      dataIndex: 'containerSpec',
      key: 'containerSpec',
      customRender: ({ text }) => {
        return h('div', cmbShowRender(text,[],'CONTAINER_MODEL'))
      }
    },
    {
      title: '集装箱数',
      minWidth: 100,
      align: 'center',
      dataIndex: 'containerCount',
      key: 'containerCount',
    },
    {
      title: '集装箱号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'containerNo',
      key: 'containerNo',
    },
    {
      title: '商品名称',
      minWidth: 200,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '箱数',
      minWidth: 150,
      align: 'center',
      dataIndex: 'boxCount',
      key: 'boxCount',
    },
    {
      title: '剩余箱数',
      minWidth: 150 ,
      align: 'center',
      dataIndex: 'remainingBoxCount',
      key: 'remainingBoxCount',
    },
    {
      title: '备注',
      minWidth: 200,
      align: 'center',
      dataIndex: 'note',
      key: 'note'
    }

  ])

  const getList = async () =>{
    tableLoading.value = true
    try {
      const res = await  window.majesty.httpUtil.postAction(`${ycCsApi.bizBpAnalyseOrderListBoxBox.list}?page=${page.current}&limit=${page.pageSize}`,{
        parentId:props.headId
      });
      dataSourceList.value = res.data
      page.total = res.total
    }catch (err){
      console.log('获取数据异常！',err)
    }finally {
      tableLoading.value = false
    }

  }






  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData: [],
    loading: false,
  });


  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };


  /* 按钮loading */
  const deleteLoading = ref(false)


  /* 返回事件 */
  const handlerOnBack = (flag) => {
    show.value = !show.value;
    // 返回清空选择数据
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    editConfig.editData = {}
    if (flag) {
      getList()
    }
  }

  const pCode = ref(null)
  onMounted(() => {
    // 获取PCode参数
    getPCode().then(res=>{
      // console.log('res',res)
      pCode.value = res;
    })
    getList()
    tableHeight.value = getTableScroll(100, '');

  })


</script>

<style lang="less" scoped>
.surely-table-bordered {
  border: 1px solid var(--surely-table-border-color);
  /* border-right: 0; */
}

</style>
