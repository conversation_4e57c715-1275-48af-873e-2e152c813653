<template>
  <section>

    <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
            :model="formData" >
      <a-card size="small" title="出库回单表头" class="cs-card-form">
        <div class="cs-form grid-container">
<!--          出库回单编号-->
          <a-form-item name="receiptNumber" :label="'出库回单编号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.receiptNumber"/>
          </a-form-item>
<!--          合同号-->
          <a-form-item name="contractNumber" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNumber"/>
          </a-form-item>
<!--          订单号-->
          <a-form-item name="orderNumber" :label="'订单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.orderNumber"/>
          </a-form-item>
<!--          提货人-->
          <a-form-item name="consignee" :label="'提货人'" class="grid-item" :colon="false">
            <cs-select
              :disabled="showDisable||showReceiptDisable"
              optionFilterProp="label"
              option-label-prop="key"
              allow-clear
              show-search
              v-model:value="formData.consignee"
              :options="consigneeList"
              :combine-display="true"
              id="consignee"
            />
<!--              <a-select-option v-for="item in buyerOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
          </a-form-item>
<!--          仓库-->
          <a-form-item name="warehouse" :label="'仓库'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable||showReceiptDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.warehouse" id="warehouse">
              <a-select-option v-for="item in warehouseList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          出库日期-->
          <a-form-item name="deliveryDate" :label="'出库日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable||showReceiptDisable"
              v-model:value="formData.deliveryDate"
              id="deliveryDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
<!--          供应商-->
          <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplier" id="supplier">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          发送用友-->
          <a-form-item name="sendUfida" :label="'发送用友'" class="grid-item" :colon="false">
            <cs-select
              :disabled="showDisable||showReceiptDisable"
              optionFilterProp="label"
              option-label-prop="key"
              allow-clear
              show-search
              :options="productClassify.isNot"
              v-model:value="formData.sendUfida"
              :combine-display="false"
              id="sendUfida"
            />
<!--            <cs-select :disabled="showDisable||showReceiptDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendUfida" id="sendUfida">-->
<!--              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
          </a-form-item>
<!--          抽检出库-->
          <a-form-item name="inspectionOutstock" :label="'抽检出库'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable||showReceiptDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.inspectionOutstock" id="inspectionOutstock">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          备注-->
          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable||showReceiptDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>
<!--          制单人-->
          <a-form-item name="createBy" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createBy"/>
          </a-form-item>
<!--          制单时间-->
          <a-form-item name="createDate" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createDate"
              id="createDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
<!--          出库单据状态-->
          <a-form-item name="outstockDocumentStatus" :label="'出库单据状态'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.outstockDocumentStatus" id="outstockDocumentStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"  :disabled="showReceiptDisable"
                      v-show="!showDisable">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>

            <!-- 确认 -->
            <a-button size="small" type="ghost"   @click="confirmOrderHead" class="cs-margin-right"
                      v-show="!showDisable" :loading="confirmOrderLoading">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu >
                  <a-menu-item key="1" @click="confirmExport">打印PDF</a-menu-item>
                  <a-menu-item key="2" @click="printOrder">打印EXCEL</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small" type="ghost">
                打印出库回单
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </div>

        </div>
      </a-card>
    </a-form>


    <a-card v-if="isShow" size="small" title="出库回单表体" class="cs-card-form" >
      <biz-i-Receipt-list-list @save="handlerSave" :head-id="formData.sid" :editConfig="props.editConfig" :stateMessage = "formData.outstockDocumentStatus"/>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {createVNode, onMounted, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  getIReceiptHeadMessage,
  getIReceiptHeadUpdate,
  getIReceiptExport,
  confirmReceiptHead
} from "@/api/cs_api_constant";
import BizIReceiptListList from "@/view/dec/imported_cigarettes/receipt/list/BizIReceiptListList.vue";
import {downFile} from "@/api/manage";
import ycCsApi from "@/api/ycCsApi";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
const {getPCode} = usePCode()


const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },

});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 定义组件名称
defineOptions({
  name: 'BizIReceiptHeadEdit'
})
const { supplierList,getSupplierList} = useOrderColumnsCommon()
getSupplierList()

// 是否禁用
const showDisable = ref(false)
const showReceiptDisable = ref(false)
const isShow = ref(false)
// 表单数据
const formData = ref({
  // 主建sid
  sid: '',
  // 出库回单编号
  receiptNumber: '',
  // 合同号
  contractNumber: '',
  // 订单号
  orderNumber: '',
  // 提货人
  consignee: '',
  // 仓库
  warehouse: '',
  // 出库日期
  deliveryDate: '',
  // 供应商
  supplier: '',
  // 发送用友
  sendUfida: '',
  // 抽检出库
  inspectionOutstock: '',
  // 备注
  remark: '',
  // 制单人
  createBy: '',
  // 制单时间
  createDate: '',
  // 出库单据状态
  outstockDocumentStatus: ''
})
// 校验规则
const rules = {
  receiptNumber: [
    {required: true}
  ],
  contractNumber: [
    {required: true}
  ],
  orderNumber: [
    {required: true}
  ],
  createBy: [
    {required: true}
  ],
  createDate: [
    {required: true}
  ],
  state: [
    {required: true}
  ],
  consignee: [
    {required: true,max: 200, message: '提货人长度不能超过200位字节', trigger: 'blur'}
  ],
  warehouse: [
    {required: true,max: 100, message: '仓库长度不能超过100位字节', trigger: 'blur'}
  ],
  deliveryDate: [
    {required: true,type: 'date', message: '请选择有效日期格式', trigger: 'change'}
  ],
  sendUfida: [
    {required: true,max: 10, message: '发送用友必填', trigger: 'blur'}
  ],
  remark: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ]
}

const pCode = ref('')
const consigneeList = ref([])
const getCustomerList = async () => {
  let prams = {
    merchantType:'0',
    commonFlag:'1'
  }
  try {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizDecCommon.getCustomerListByType,prams)
    consigneeList.value = res.data.customerOneList
  }catch (err){
    console.log('获取客户信息失败，',err)
  }finally {

  }

}
// 初始化操作
onMounted(() => {
  getCustomerList()
  getPCode().then(res => {
    pCode.value = res;
  })
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  //初始化数据
  getList()
  getWarehouseOptions()
});
const warehouseList = ref([])
const getWarehouseOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.storehouse.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        warehouseList.value.push({
          value: item.paramCode,
          label: item.storehouseName
        });
      });
    } else {
      message.error(res.message || '获取仓库数据失败');
    }
  } catch (error) {
    message.error('获取仓库数据失败');
  }
}

const buyerOptions = ref([]);

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.value.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败1');
  }
}
const getList = () => {
  var json = {'headId':props.editConfig.editData.sid}
  getIReceiptHeadMessage(json).then((res) => {
    if (res.code === 200) {
      if(res.data){
        formData.value = res.data
        console.log(formData.value.outstockDocumentStatus)
        if(formData.value.outstockDocumentStatus === '1' || formData.value.outstockDocumentStatus === '2'){
          showReceiptDisable.value = true
        }else{
          showReceiptDisable.value = false
        }
      }
      console.log('res',res.data)
      isShow.value = true
    }
  })
  getBuyerOptions()
}

// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  if(showReceiptDisable.value === true){
    return message.error('仅编制状态可编辑!')
  }
  formRef.value
    .validate()
    .then(() => {
      console.log("-------------")
      console.log(formData.value.sid)
      if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        getIReceiptHeadUpdate(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('表头保存成功!')
            formData.value = res.data
            console.log('res',res.data)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};


/* 出库表头确认 */
const confirmOrderLoading = ref(false)
const confirmOrderHead = async ()=>{
  if(formData.value.outstockDocumentStatus === '2'){
    return message.error('作废无法确认!')
  }
  try {
    await formRef.value.validate()
    try {
      if(!showReceiptDisable.value){
        handlerSave()
      }
    }finally {
      // 弹出确认框
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '确认执行此操作吗？',
        onOk() {

          // 这里需要调用确认API
          const params = {
            sid : props.editConfig.editData.sid
          }
          confirmReceiptHead(params).then(res => {
            if (res.code === 200) {
              message.success(res.message)
            }else{
              message.error(res.message)
            }
          }).finally(() => {
            getList()
          })
        },
        onCancel() {
          // 取消操作
        },
      });
    }
  }catch (error) {
    return message.error('请先保存出库回单表头!')
  }
}

const  confirmExport = ()=>{
  pdfExport()
}

const pdfExport = () => {
  const params = {sid:formData.value.sid,sType:'pdf'}
  console.log(formData.value.sid,'sid?')
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.bizIReceiptHead.export}/${formData.value.sid}/pdf`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}
const printOrder = ()=>{
  const params = {sid:formData.value.sid,sType:'excel'}
  console.log(formData.value.sid,'sid?')
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.bizIReceiptHead.export}/${formData.value.sid}/excel`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}




</script>

<style lang="less" scoped>


</style>



