<template>
  <section>

    <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
            :model="formData" >
      <a-card size="small" title="入库回单表头" class="cs-card-form">
        <div class="cs-form grid-container">
<!--          入库回单编号-->
          <a-form-item name="warehouseReceiptNumber" :label="'入库回单编号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.warehouseReceiptNumber"/>
          </a-form-item>
<!--          合同号-->
          <a-form-item name="contractNumber" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNumber"/>
          </a-form-item>
<!--          订单号-->
          <a-form-item name="orderNo" :label="'订单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.orderNo"/>
          </a-form-item>
          <!--          进仓编号-->
          <a-form-item name="warehouseEntryNumber" :label="'进仓编号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.warehouseEntryNumber"/>
          </a-form-item>
          <!--          提货单号-->
          <a-form-item name="ladingNumber" :label="'提货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.ladingNumber"/>
          </a-form-item>
<!--          提货单位  -->
          <a-form-item name="ladingDepartment" :label="'提货单位'" class="grid-item" :colon="false">
            <cs-select
              :disabled="showDisable"
              optionFilterProp="label"
              option-label-prop="key"
              allow-clear
              show-search
              :combine-display="true"
              :options="ladingDepartmentList"
              v-model:value="formData.ladingDepartment"
              id="ladingDepartment"
            />
<!--            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search-->
<!--                       v-model:value="formData.ladingDepartment" id="seller">-->
<!--              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"-->
<!--                               :value="item.value" :label="item.value + item.label">-->
<!--                {{ item.value }} {{ item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
          </a-form-item>
          <!--          进口发票号码
          <a-form-item name="invoiceNumber" :label="'进口发票号码'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.invoiceNumber"/>
          </a-form-item>-->
          <!--          供应商-->
          <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.supplier" id="supplier">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>

          </a-form-item>

<!--          仓库-->
          <a-form-item name="warehouse" :label="'仓库'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.warehouse" id="seller">
              <a-select-option v-for="item in storehouseOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!--          币种-->
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.curr"/>
          </a-form-item>
          <!--          卖出价（汇率）-->
          <a-form-item name="sellingRate" :label="'卖出价（汇率）'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.sellingRate"/>
          </a-form-item>
          <!--          汇率-->
          <a-form-item name="rate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.rate"/>
          </a-form-item>
          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.businessDate"
              id="createrTime"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!--          税单日期-->
          <a-form-item name="taxInvoiceDate" :label="'税单日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.taxInvoiceDate"
              id="taxInvoiceDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <!--          进仓日期-->
          <a-form-item name="entryDate" :label="'进仓日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.entryDate"
              id="entryDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

<!--          出库日期-->

          <a-form-item name="outdate" :label="'出库日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.outdate"
              id="outdate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <!--          客户折扣率-->
          <a-form-item name="discountRate" :label="'客户折扣率'" class="grid-item" :colon="false">
            <!-- <a-input :disabled="true" size="small"  v-model:value="formData.discountRate"/>-->

           <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small" v-model:value="formData.discountRate"
                           :formatter="value => `${value}%`"
                           :parser="value => value.replace('%', '')"
                           notConvertNumber decimal int-length="19" precision="6"
           />
         </a-form-item>

<!--          发送用友-->
          <a-form-item name="sendToYongyou" :label="'发送用友'" class="grid-item" :colon="false">
            <cs-select
              :disabled="showDisable"
              optionFilterProp="label"
              option-label-prop="key"
              allow-clear
              show-search
              :options="productClassify.isNot"
              v-model:value="formData.sendToYongyou"
              :combine-display="false"
              id="sendToYongyou"
            />
<!--            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendToYongyou" id="sendUfida">-->
<!--              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
          </a-form-item>

<!--          备注-->
          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>
<!--          制单人-->
          <a-form-item name="insertUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.insertUserName"/>
          </a-form-item>
<!--          制单时间-->
          <a-form-item name="insertTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.insertTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
<!--          入库单据状态-->
          <a-form-item name="status" :label="'入库单据状态'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="outstockDocumentStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-dropdown v-show="props.editConfig.editStatus !== 'SHOW' ">
              <template #overlay>
                <a-menu >
                  <a-menu-item key="1" @click="onPrint('.pdf')">打印PDF</a-menu-item>
                  <a-menu-item key="2" @click="onPrint('.xlsx')">打印EXCEL</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small" type="ghost">
                打印进仓通知
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
                <DownOutlined />
              </a-button>
            </a-dropdown>


            <a-dropdown v-show="props.editConfig.editStatus !== 'SHOW' ">
              <template #overlay>
                <a-menu >
                  <a-menu-item key="1" @click="onPrintOfLading('.pdf')">打印PDF</a-menu-item>
                  <a-menu-item key="2" @click="onPrintOfLading('.xlsx')">打印EXCEL</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small" type="ghost">
                打印提货单
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
                <DownOutlined />
              </a-button>
            </a-dropdown>

            <!--
            <a-button size="small" type="ghost"  @click="onPrint" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              </template>
              <template #default>
                打印进仓通知(EXCEL)
              </template>
            </a-button>
            <a-button size="small" type="ghost"  @click="onPrintOfLading" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">
              <template #icon>
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
              </template>
              <template #default>
                打印提货单(EXCEL)
              </template>
            </a-button>-->
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" :disabled="formData.status  !== '0'"
                      v-show="props.editConfig.editStatus === editStatus.EDIT " :loading="saveLoading">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <!-- 确认 -->
            <a-button size="small" type="ghost"   @click="onSure" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.EDIT" :loading="confirmOrderLoading">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>


            <!-- 提取税金 -->
<!--            <a-button size="small" type="ghost"   @click="extractTax" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== 'SHOW' " :loading="extractTaxLoading">-->
<!--              <template #icon>-->
<!--                <GlobalIcon class="btn-icon" type="dollar"  style="font-size: 12px;"/>-->
<!--              </template>-->
<!--              <template #default>-->
<!--                提取税金-->
<!--              </template>-->
<!--            </a-button>-->
            <!-- 	打印入库回单 -->
            <div class="print-receipt-container" style="display: inline-block; margin-right: 8px;">
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handlePrintMenuClick">
                    <a-menu-item key="pdf">打印PDF</a-menu-item>
                    <a-menu-item key="xlsx">打印EXCEL</a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small" type="ghost" :loading="printFileLoading" class="cs-margin-right">
                  <template #icon>
                    <GlobalIcon class="btn-icon" type="cloud-download" style="font-size: 12px;"/>
                  </template>
                  <template #default>
                    打印入库回单
                  </template>
                </a-button>
              </a-dropdown>
            </div>
          </div>

        </div>
      </a-card>
    </a-form>


    <a-card v-if="isShow" size="small" title="入库回单表体" class="cs-card-form" >
      <biz-i-warehouse-receipt-list ref="bizReceiptList" :head-id="formData.sid" :edit-config="editConfig" :status="formData.status" :operationStatus="props.operationStatus"/>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {h, onMounted, reactive, ref,watch,createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  confirmIOrderHead,
  getIWarehouseReceiptHeadMessage,
  getIWarehouseReceiptHeadUpdate
} from "@/api/cs_api_constant";
import BizIWarehouseReceiptList from "@/view/dec/imported_cigarettes/warehouse-receipt/list/BizIWarehouseReceiptList.vue";
import ycCsApi from "@/api/ycCsApi";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import useEventBus from "@/view/common/eventBus";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
const { emitEvent } = useEventBus()
const {getPCode} = usePCode()

import {getOrderSupplierList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
/**
 * 数据列表
 */
const supplierList = ref([])


const bizReceiptList = ref(null);

const saveLoading = ref(false)

/* 转换Columns配置 将关键属性转为 key,value形式，并且过滤操作等属性 */
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default: () => ''
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 定义组件名称
defineOptions({
  name: 'warehouseReceiptHeadEdit'
})


// 是否禁用
const showDisable = ref(false)
const isShow = ref(false)
// 表单数据
const formData = ref({
  // 主建sid
  sid: '',
  // 入库回单编号
 warehouseReceiptNumber: '',
  // 合同号
  contractNumber: '',
  // 订单号
  orderNo: '',
  // 进仓编号
  warehouseEntryNumber: '',
  // 提货单号
  ladingNumber: '',
  // 提货单位
  ladingDepartment: '',
  // 进口发票号码
  invoiceNumber: '',
  // 供应商
  supplier: '',
  // 仓库
  warehouse: '',
  // 币种
  curr: '',
  // 出价（汇率）
  sellingRate: '',
  // 汇率
  rate: '',
  businessDate: '',
// 税单日期
  taxInvoiceDate: '',
  // 进仓日期
  entryDate: '',
  // 出库日期
  outdate: '',
  // 客户折扣率
  discountRate: '',
  // 发送用友
  sendToYongyou: '',
  // 备注
  note: '',
  // 制单人
  insertUserName: '',
  // 制单时间
  insertTime: '',
  // 入库单据状态
  status: '',
  // 入库回单表体
  warehouseReceiptListParams: []
})
// 校验规则
const rules = {
  warehouseReceiptNumber: [
    {required: true, message: '入库回单编号不能为空', trigger: 'change'},
    {max: 60, message: '入库回单编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  contractNumber: [
    {required: true, message: '合同号不能为空', trigger: 'change'},
    {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
  ],
  orderNo: [
    {required: true, message: '订单号不能为空', trigger: 'change'},
    {max: 60, message: '订单号长度不能超过 60位字节', trigger: 'blur'}
  ],

  warehouseEntryNumber: [
    {required: true, message: '进仓编号不能为空', trigger: 'change'},
    {max: 60, message: '进仓编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  ladingNumber: [
    {required: true, message: '提货单号不能为空', trigger: 'change'},
    {max: 60, message: '提货单号长度不能超过 60位字节', trigger: 'blur'}
  ],

  ladingDepartment: [
    {required: true, message: '提货单位不能为空', trigger: 'change'},
    {max: 200, message: '提货单位长度不能超过 200位字节', trigger: 'blur'}
  ],


  supplier: [
    {required: true, message: '供应商不能为空', trigger: 'change'},
    {max: 200, message: '供应商长度不能超过 200位字节', trigger: 'blur'}
  ],

  warehouse: [
    {required: true, message: '仓库不能为空', trigger: 'change'},
    {max: 100, message: '仓库长度不能超过 100位字节', trigger: 'blur'}
  ],
  curr: [
    {required: true, message: '币种不能为空', trigger: 'change'},
    {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
  ],
  sellingRate: [
    {required: true, message: '卖出价（汇率）不能为空', trigger: 'change'},
  ],
  rate: [
    {required: true, message: '汇率不能为空', trigger: 'change'},
  ],
  businessDate: [
    {required: true, message: '业务日期不能为空', trigger: 'change'},
  ],
  sendToYongyou: [
    {required: true, message: '发送用友不能为空', trigger: 'change'},
  ],

  insertTime: [
    {required: true, message: '制单时间不能为空', trigger: 'change'},
  ],

  insertUserName: [
    {required: true, message: '制单人不能为空', trigger: 'change'},
  ],
  status: [
    {required: true, message: '入库单据状态不能为空', trigger: 'change'},
  ]
}

const pCode = ref('')
// 获取客户信息
const ladingDepartmentList = ref([])
const getCustomerList = async () => {
  let prams = {
    merchantType:'0',
    commonFlag:'1'
  }
  try {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizDecCommon.getCustomerListByType,prams)
    ladingDepartmentList.value = res.data.customerOneList
  }catch (err){
    console.log('获取客户信息失败，',err)
  }finally {

  }

}
// 初始化操作
onMounted(() => {
  // 获取客户信息
  getCustomerList()
  //供应商
  getSupplierList();
  //获取客商信息
  getBuyerOptions();
  getStorehouseOptions();
  getPCode().then(res => {
    pCode.value = res;
  })
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  var json = {parentId:props.editConfig.editData.sid}
  //初始化数据
  getIWarehouseReceiptHeadMessage(json).then((res) => {
    if (res.code === 200&&res.data!=null) {
      // formData.value = res.data
      Object.assign(formData.value,res.data)
      isShow.value = true
      //状态为确定后,不可编辑
      if (formData.value.status=='1'||formData.value.status=='2'){
        showDisable.value = true
      }

      // 如果业务日期为空，设置为当前日期
      if (!formData.value.businessDate) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        formData.value.businessDate = `${year}-${month}-${day}`;
      }
    }
  })
});


// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
        saveLoading.value = true
        // 保存数据
        Object.assign(formData.value,{
          warehouseReceiptListParams:bizReceiptList.value.dataSourceList
        })
        console.log('进货信息表体ref:', formData.value.warehouseReceiptListParams[0].foreignPrices)
        getIWarehouseReceiptHeadUpdate(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('修改成功!')
           // formData.value = res.data
            Object.assign(formData.value,res.data)
            onBack({
              editData: null,
              showBody: true,
              editStatus: editStatus.EDIT,
              showBodyWarehouseReceiptHead:true,
              showBodyPurchaseHead:true,
            })
          }else {
            message.error(res.message)
          }
        }).finally(() => {
          // 保存后，刷新列表
          // getList()
          emitEvent('refreshBizIWarehouseReceiptList')
          saveLoading.value = false

        })

    })
    .catch(error => {
      console.log('validate failed', error);
    })
};


watch(
  () => formData.value.supplier, // 监听的具体属性
  (newValue, oldValue) => {
    if (newValue=='中烟英美烟草国际有限公司'){
      //formData.value.ladingDepartment='北京中烟三五品牌营销有限公司'
    }else {
      //formData.value.ladingDepartment='上海烟草贸易中心有限公司'
    }
  }
);

//基础资料-客商信息
const buyerOptions = reactive([])
const storehouseOptions = reactive([])
const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        // 只获取lable为：上海烟草贸易中心有限公司、北京中烟三五品牌营销有限公司的数据
        if (item.merchantNameCn === '上海烟草贸易中心有限公司' || item.merchantNameCn === '北京中烟三五品牌营销有限公司') {
          buyerOptions.push({
            value: item.merchantCode,
            label: item.merchantNameCn
          });
        }
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const getStorehouseOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.storehouse.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        storehouseOptions.push({
          value: item.paramCode,
          label: item.storehouseName
        });
      });
    } else {
      message.error(res.message || '获取仓库数据失败');
    }
  } catch (error) {
    message.error('获取仓库数据失败');
  }
}

/* 表头确认 */
const confirmOrderLoading = ref(false)
const onSure = ()=>{
  if (formData.value.status === '1'){
    message.warning('该数据已经确认，无需重复操作！')
    confirmOrderLoading.value = false

    return
  }
  if (formData.value.status === '2'){
    message.warning('该数据已经作废，无需重复操作！')
    confirmOrderLoading.value = false

    return
  }
  formRef.value
    .validate()
    .then(() => {
      // 弹出确认框
      Modal.confirm({
        title: '提醒?',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '是否确认所选项？',
        onOk() {
          confirmOrderLoading.value = true
          const params = {sid:formData.value.sid}
          window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptHead.onSure, params).then(res => {
            Object.assign(formData.value,res.data)
            if (res.code === 200){
              message.success(res.message)
              confirmOrderLoading.value = false
              onBack({
                showBody: true,
                editData:null,
                editStatus: editStatus.EDIT,
                showBodyReceiptSell: true,
                showBodyWarehouseReceiptHead:true,
                showBodyPurchaseHead:true
              })
            } else {
              message.error(res.message)
              confirmOrderLoading.value = false
              onBack({
                showBody: true,
                editData:null,
                editStatus: editStatus.EDIT,
                showBodyReceiptSell: false,
                showBodyWarehouseReceiptHead:true,
                showBodyPurchaseHead:true
              })
            }
            if (formData.value.status=='1'||formData.value.status=='2'){
              //成功后不可以编辑
              showDisable.value = true
            }
          }).catch(() => {
          }).finally(() => {

          })
        },
        onCancel() {
          confirmOrderLoading.value = false
        },
      });
    })
    .catch(error => {
      console.log('validate failed', error);
      confirmOrderLoading.value = false

    })
}



/* 提取税金 */
const extractTaxLoading = ref(false)
const printFileLoading = ref(false)
const extractTax = ()=>{
  extractTaxLoading.value = true
  // 这里预留调用后端接口的位置
  // 可以参考类似的接口调用方式，例如：
  const params = {
    parentId: formData.value.sid,
  }
  window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptList.extractTaxes, params)
    .then(res => {
      if (res.code === 200) {
        message.success('提取税金成功!')
        // 刷新表体列表
        bizReceiptList.value.reloadData()
      } else {
        message.error(res.message || '提取税金失败')
      }
    })
    .catch(error => {

    })
    .finally(() => {
      extractTaxLoading.value = false
    })

}




/*打印进仓通知书*/
 const onPrint = (type) => {
   const params = {sid:formData.value.sid,type:type}

   window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptHead.checkPrintNotice, params)
     .then(res => {
       if (res.data === true) {
         window.majesty.httpUtil.downloadFile(
           `${ycCsApi.bizIWarehouseReceiptHead.printNotice}`, null,params,'post',null
         ).then(res => {
         }).catch(() => {
         }).finally(() => {
         })
       } else {
         message.error(res.message)
       }
     })
     .catch(error => {

     })
     .finally(() => {
     })

}
/*打印提货单*/
const onPrintOfLading = (type) => {
  const params = {sid:formData.value.sid,type:type}
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.bizIWarehouseReceiptHead.onPrintOfLading}`, null,params,'post',null
  ).then(res => {
  }).catch(() => {
  }).finally(() => {
  })
}



// 处理打印菜单点击事件
const handlePrintMenuClick = (e) => {
  const printType = e.key; // 'pdf' or 'xlsx'
  printReceipt(printType);
}

// 打印入库回单（支持PDF和XLSX格式）
const printReceipt = (fileType) => {
  printFileLoading.value = true;
  // 将文件类型直接拼接到URL路径中
  const url = `${ycCsApi.bizIWarehouseReceiptHead.print}/${formData.value.sid}/${fileType}`;
  console.log(formData.value.sid, 'sid?', fileType, 'fileType', url);

  window.majesty.httpUtil.downloadFile(
    url, null, null, 'post', null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
    message.error(`打印${fileType === 'xlsx' ? 'XLSX' : 'PDF'}失败`);
  }).finally(() => {
    printFileLoading.value = false;
  })
}

// 保留原函数以兼容可能的其他调用
const printReceiptPdf = () => {
  printReceipt('pdf');
}


</script>

<style lang="less" scoped>


</style>



