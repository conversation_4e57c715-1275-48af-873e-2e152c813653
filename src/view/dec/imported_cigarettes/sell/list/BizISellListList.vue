 <template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">

      <!-- 表格区域 -->
      <div>
        <a-spin :spinning="false">
          <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          row-key="sid"
          :row-height="30"
        >
          <!-- 看下面注释 -->
          <template v-if="props.editConfig.editStatus !=='show'&&stateMessage === '0'"
                    #cellEditor="{ column, modelValue,record }">
            <template v-if="!showDisable && ( column.dataIndex === 'amountOfTax')">
              <a-input
                size="small"
                v-model:value="modelValue.value"
                @blur="handleEnterAmountOfTax(record,modelValue.value)"
                @keydown.enter="handleEnterAmountOfTax(record,modelValue.value)"
                style="width: 100%"
                @input="handleInput($event, modelValue)"
                placeholder="请输入最多17位整数和2位小数"
              />
            </template>
            <template v-if="!showDisable && (  column.dataIndex === 'taxNotIncluded')">
              <a-input
                size="small"
                v-model:value="modelValue.value"
                @blur="handleEnterTaxNotIncluded(record,modelValue.value)"
                @keydown.enter="handleEnterTaxNotIncluded(record,modelValue.value)"
                style="width: 100%"
                @input="handleInput($event, modelValue)"
                placeholder="请输入最多17位整数和2位小数"
              />
            </template>
            <template v-if="props.editConfig.editStatus !=='show' && (column.dataIndex === 'salesContractNumber')">
              <a-input
                size="small"
                v-model:value="modelValue.value"
                @blur="handleEnterSalesContractNumber(record,modelValue.value)"
                @keydown.enter="handleEnterSalesContractNumber(record,modelValue.value)"
                style="width: 100%"
                maxlength="60"
                placeholder="请输入最多60位内容"
              />
            </template>
            <template v-if="props.editConfig.editStatus !=='show' && (column.dataIndex === 'salesInvoiceNumber')">
              <a-input
                size="small"
                v-model:value="modelValue.value"
                @blur="handleEnterSalesInvoiceNumber(record,modelValue.value)"
                @keydown.enter="handleEnterSalesInvoiceNumber(record,modelValue.value)"
                style="width: 100%"
                maxlength="60"
                placeholder="请输入最多60位内容"
              />
            </template>
          </template>
        </s-table>
        </a-spin>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="cs-margin-right cs-list-total-data ">
          税额：{{sumData.amountOfTax}} ，不含税金额：{{sumData.taxNotIncluded}}，价税合计：{{sumData.totalValueTax}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>


  </section>


</template>

<script setup>

import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/dec/imported_cigarettes/sell/list/BizISellListColumns";
import {message, Modal} from "ant-design-vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import CheckOutlined from "@ant-design/icons-vue/lib/icons/CheckOutlined";
import EditOutlined from "@ant-design/icons-vue/lib/icons/EditOutlined";
const { totalColumns } = getColumns()
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import {editStatus} from "@/view/common/constant";
import BizIOrderHeadEdit from "@/view/dec/imported_cigarettes/head/BizIOrderHeadEdit.vue";
import BizIOrderListEdit from "@/view/dec/imported_cigarettes/list/BizIOrderListEdit.vue";
import { getITotal, getISellListUpdate} from "@/api/cs_api_constant";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import {FormItem} from "view-ui-plus";
import {getSellInvoiceNoSumData} from "@/api/cs_api_constant";




/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  dataSourceList,
  tableLoading,
  getTableScroll,
  doExport,
} = useCommon()



defineOptions({
  name: 'BizIOrderListList',
});


const props = defineProps({
  headId:{
    type:String,
    default:()=>''
  },
  editConfig:{
    type:Object,
    default:()=>null
  },
  stateMessage:{
    type:Object,
    default:()=>null
  },
})
// 声明事件
const dataSource = ref([]);
const tableRef = ref(null);
const sumData = reactive({
  amountOfTax:0,
  taxNotIncluded:0,
  totalValueTax:0
})
const editableData  = reactive({});
// 定义自定义事件
const emit = defineEmits(['child-to-parent','save']);

// 暴露方法给父组件
defineExpose({
  getList,
  getListUpdate
});

/* 获取进货信息表体汇总数据 */
const getSumData = async ()=>{
  if (isNullOrEmpty(props.headId)){
    // console.log('headId',props.headId)
    return
  }
  const res = await getSellInvoiceNoSumData({headId:props.headId})
  if (res.code === 200) {
    Object.assign(sumData,res.data)
  }else {
    message.error(res.message)
  }

}

const handleInput = (event, modelValue) => {
  const value = event.target.value;
  const regex = /^\d{0,17}(\.\d{0,2})?$/;

  if (!regex.test(value)) {
    // 直接截断非法输入
    event.target.value = value.slice(0, -1);
    modelValue.value = event.target.value; // 同步到模型
  }
};

const edit = (key) => {
  // console.log('key',key)
  editableData[key] = true;
};


const entry = (key) => {
  // console.log('key',key)
};

const save = (key) => {
  // console.log('key',key)
  delete editableData[key];
};
const showDisable = ref(false)

function getListUpdate(){
  show.value = false
  getList()
}

/* 查询数据 */
function getList() {
  tableLoading.value = true
  window.majesty.httpUtil.postAction(`${ycCsApi.bizISellList.selectList}?page=${page.current}&limit=${page.pageSize}`,
    {headId:props.headId}
  ).then(res => {
    dataSourceList.value = res.data
    page.total = res.total
    // 数据加载完成后，如果不是查看模式，触发编辑器
    // setTimeout(() => {
    triggerEditor();
    // }, 200);
  }).finally(() => {
    show.value = true
    tableLoading.value = false
    gridData.selectedData = [];
    gridData.selectedRowKeys = [];
    getSumData()
  })
}
// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && dataSourceList.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});

// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...dataSourceList.value];
    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'amountOfTax', rowKey: row.sid });
      editConfigs.push({ columnKey: 'taxNotIncluded', rowKey: row.sid });
      editConfigs.push({ columnKey: 'salesInvoiceNumber', rowKey: row.sid });
      editConfigs.push({ columnKey: 'salesContractNumber', rowKey: row.sid });
    });

    // 使用nextTick确保DOM已更新

      // console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        // console.log(tableRef)
        // 一次性打开所有单元格的编辑状态
        // 只关闭 columnKey: 'amountOfTax' 列的编辑状态
        tableRef.value?.closeEditor(editConfigs.filter(item => item.columnKey === 'amountOfTax'));
        setTimeout(()=>{
          // 恢复鼠标点击的焦点
          tableRef.value?.openEditor(editConfigs);
        },250)
      }

  }
};
const onPageChange = (pageNumber, pageSize)=> {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getList()
}


onMounted(fn => {
  tableHeight.value = getTableScroll(100,'');
  getList()
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  getSumData()
})

const tableHeight = ref('')

/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;

  // 触发自定义事件，并传递数据
  emit('child-to-parent', selectedRowKeys);
};


/* 按钮loading */
const deleteLoading = ref(false)



/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  // 返回清空选择数据
  gridData.selectedData = [];
  gridData.selectedRowKeys = [];
  editConfig.editData = {}
  if (flag){
    getList()
  }
}

/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  show.value =!show.value;
}






/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  // closeEditor();
};


/*
  点击回车触发行内编辑事件

  export interface EditableValueParams<RecordType = DefaultRecordType, TValue = any> {
    value: TValue;
    record: RecordType;
    recordIndexs: number[];
    column: ColumnType<RecordType>;
  }

  export interface ValueParserParams<RecordType = DefaultRecordType, TValue = any> {
    newValue: TValue;
    oldValue: TValue;
    record: RecordType;
    recordIndexs: number[];
    column: ColumnType<RecordType>;
  }

  export interface ValueParserFunc<T = any, TValue = any> {
    (params: ValueParserParams<T, TValue>): TValue | null | undefined;
  }

  export interface ValueGetterFunc<T = any, TValue = any> {
    (params: EditableValueParams<T, TValue>): string | null | undefined;
  }
  export interface CellEditorArgs {
    modelValue: Ref<any>;
    save: () => void;
    onInput: (event: Event, value: any) => void;
    closeEditor: () => void;
    column: ColumnType;
    editorRef: Ref<any>;
    getPopupContainer: () => HTMLElement;
    record: DefaultRecordType; // 4.2.0
    recordIndexs: number[]; // 4.2.0
  }

  export type EditableTrigger = 'click' | 'dblClick' | 'contextmenu';

 */
const handleEnterSalesContractNumber = (record,newValue) => {
  if (newValue === record.salesContractNumber) {
    return;
  }
  record.salesContractNumber = newValue
  // console.log('editorRef',record)
  // emit('save');
  getISellListUpdate(record.sid,record).then(res => {
    if (res.code === 200) {
      // message.success(res.message)
    }else{
      message.error(res.message)
    }
  }).finally(() => {
    getList()
    getSumData()
  })
};
const handleEnterSalesInvoiceNumber = (record,newValue) => {
  if (newValue === record.salesInvoiceNumber) {
    return;
  }
  record.salesInvoiceNumber = newValue
  // console.log('editorRef',record)
  // emit('save');
  getISellListUpdate(record.sid,record).then(res => {
    if (res.code === 200) {
      // message.success(res.message)
    }else{
      message.error(res.message)
    }
  }).finally(() => {
    getList()
    getSumData()
  })
};
const handleEnterAmountOfTax = (record,newValue) => {
  // console.log('编辑税额',record,newValue)
  if (newValue === record.amountOfTax) {
    return;
  }
  record.amountOfTax = newValue
  // console.log('editorRef',record)
  // emit('save');
  window.majesty.httpUtil.postAction(`${ycCsApi.bizISellList.updateAmountOfTax}`,record).then(res=>{
    if (res.code === 200) {
      // message.success(res.message)
    }else{
      message.error(res.message)
    }
  }).finally(() => {
    getList()
    getSumData()
  })
};
const handleEnterTaxNotIncluded = (record,newValue) => {
  if (newValue === record.taxNotIncluded) {
    return;
  }
  record.taxNotIncluded = newValue
  // console.log('editorRef',record)
  // emit('save');
  getISellListUpdate(record.sid,record).then(res => {
    if (res.code === 200) {
      // message.success(res.message)
    }else{
      message.error(res.message)
    }
  }).finally(() => {
    getList()
    getSumData()
  })
};


/* 导出事件 */
const handlerExport = () =>{
  doExport('测试文件导出.xlsx',totalColumns)
}






</script>

<style lang="less" scoped>
.editable-cell {
  position: relative;
  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: inline-block;
    // display: none;
  }

  .editable-cell-icon-check {
    margin-top: 8px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  background: #fff;
  box-sizing: border-box; /* 确保 padding 不会撑大容器 */
  // min-height: calc(100vh - 300px);
  min-height: calc(40vh);
  height: auto;
  .surely-table-body{
    // min-height: calc(100vh - 300px);
    min-height: calc(40vh);
  }
}


</style>
