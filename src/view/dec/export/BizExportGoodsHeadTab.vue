<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header" v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle">
        <a-tab-pane key="headTab" tab="出货信息">
          <biz-export-goods-head-edit ref="headTab" :edit-config="editConfig" @on-back="editBack"  :operation-status="editConfig.editStatus" />
        </a-tab-pane>
        <!-- 外销发票 -->
        <a-tab-pane key="invoiceTab" tab="外销发票" v-if="showInvoiceTab">
          <biz-export-goods-sell-head-edit :edit-config="editConfig" :head-id="headId"  :operation-status="editConfig.editStatus" @on-back="editBack" :is-all-confirmed="isAllConfirmed" />
        </a-tab-pane>
        <a-tab-pane key="documentTab" tab="准运与到货" v-if="showBody">
          <biz-export-goods-document ref="documentTab" :editConfig="editConfig" :is-all-confirmed="isAllConfirmed" @on-back="editBack" />
        </a-tab-pane>
        <a-tab-pane key="attachTab" tab="归档附件" v-if="showBody">
          <biz-export-goods-attach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig" :is-all-confirmed="isAllConfirmed" />
        </a-tab-pane>
        <!-- 审核界面 -->
        <a-tab-pane key="auditTab" tab="审核界面" v-if="showBody">
          <biz-export-goods-audit :head-id="headId" :edit-config="editConfig" :operation-status="editConfig.editStatus" @on-back="editBack"></biz-export-goods-audit>
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000" />
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>

<script setup>
import {onMounted, ref} from 'vue';
import BizExportGoodsHeadEdit from '@/view/dec/export/head/BizExportGoodsHeadEdit.vue';
import {GlobalIcon} from '@/components/icon';
import {editStatus} from "@/view/common/constant";
import BizExportGoodsDocument from "@/view/dec/export/document/BizExportGoodsDocument.vue";
import BizExportGoodsAttach from "@/view/dec/export/attach/BizExportGoodsAttch.vue";
import BizExportGoodsSellHeadEdit from "@/view/dec/export/invoice/BizExportGoodsSellHeadEdit.vue";
import BizExportGoodsAudit from "@/view/dec/export/audit/BizExportGoodsAudit.vue";
import {isNullOrEmpty} from "@/view/utils/common";
import useEventBus from "@/view/common/eventBus";

const {onEvent} = useEventBus()

  defineOptions({
    name: 'BizExportGoodsHeadTab',
  });

  const emit = defineEmits(['onEditBack']);

  const props = defineProps({
    editConfig: {
      type: Object,
      default: () => ({}),
    },
  });

  const tabBarStyle = {
    background: '#fff',
    position: 'sticky',
    top: '0',
    zIndex: '100',
  };

  const tabName = ref('headTab');
  const headId = ref('');
  // 是否显示子模块
  const showBody = ref(false)
  const isAllConfirmed = ref(false)
  const showInvoiceTab = ref(false)

  /* 返回tab界面 */
  const editBack = (val) => {
    // console.log('val', val)
    if (val.editStatus === editStatus.EDIT){
      showBody.value = val.showBody
      showInvoiceTab.value = val.showInvoiceTab
      if(val.editData != null){
        headId.value =  val.editData.id
        props.editConfig.editStatus = val.editStatus
        props.editConfig.editData = val.editData
        // 这个界面只有一个确认
        if (val.editData.invoiceDataState === '1') {
          isAllConfirmed.value = true
        }
      }
    } else {
      // 如果val === false 返回进行刷新界面
      emit('onEditBack', val)
    }
  }

  onMounted(()=>{
    // console.log('props.editConfig', props.editConfig)
    if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
      headId.value = ''
      if (props.editConfig.editData) {
        props.editConfig.editData = {}
      }
      showBody.value = false
    } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      showBody.value = true
      // console.log('编辑：',props.editConfig.editData)
      if (props.editConfig.editData.dataState === '1' ){
        showInvoiceTab.value = true
      }
      headId.value = props.editConfig.editData.id
    }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
      headId.value = props.editConfig.editData.id
      showBody.value = true
      if (props.editConfig.editData.dataState === '1' ){
        showInvoiceTab.value = true
      }
    }
    isAllConfirmed.value = false

    if (props.editConfig.editData.dataState === '1' && props.editConfig.editData.invoiceDataState === '1') {
      isAllConfirmed.value = true
    }

    // 判断只有当用户点击保存时 显示 【外销发票】、【准运与到货】、【归档附件】、【审核界面】
    showBody.value = !isNullOrEmpty(props.editConfig.editData.updateTime);

    onEvent('update_export_all',()=>{
      isAllConfirmed.value = true
    })

    /* 外销发票确认后 不能再进行编辑 */
    onEvent('export_confirm_invoice',(val)=>{
      isAllConfirmed.value = val
    })
  })


</script>

<style lang="less" scoped>

</style>
