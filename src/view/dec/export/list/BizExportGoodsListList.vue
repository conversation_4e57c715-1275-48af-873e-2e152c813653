<template>
  <!-- 第9条线-非国营贸易出口辅料-出货信息表体（商品信息） -->
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:add']">
            <a-button size="small" @click="handlerSelectContract" :disabled="!props.isEdit">
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:smoke_machine:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete" :disabled="!props.isEdit">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          :key="`table-${dataVersion.value}-${dataSourceList.length}`"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :height="500"
          :scroll="{ y: '100%',x:400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading || deleteLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
          :animate-rows="false"
        >
          <!-- 空数据                  :precision="6"-->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <template #bodyCell="{text, record, index, column, key }">
            <!-- 数量 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'qty'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].qty"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 单价 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'price'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].price"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


          <!-- 起始箱号 -->
          <template v-if="(!props.showDisable) && column.dataIndex === 'boxStartNo'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].boxStartNo"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
              :precision="0"
            />
            <span v-else>{{text}}</span>
          </template>

          <!-- 终止箱号 -->
          <template v-if="(!props.showDisable) && column.dataIndex === 'endStartNo'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].endStartNo"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
              :precision="0"
            />
            <span v-else>{{text}}</span>
          </template>



           <!-- 净重 -->
           <template v-if="(!props.showDisable) && column.dataIndex === 'netWeight'">
            <a-input-number
              v-if="props.isEdit === true"
              size="small"
              v-model:value="dataSourceList[index].netWeight"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
              style="width: 100%;height: 24px"
              @blur="() => {
                handlerTableInnerChange(record,column);
              }"
              @keydown.enter="() => {
                handlerTableInnerChange(record,column);
              }"
            />
            <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
          </template>



            <!-- 毛重 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'grossWeight'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].grossWeight"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改长 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eLength'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eLength"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改宽 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eWidth'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eWidth"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <!-- 修改高 -->
            <template v-if="(!props.showDisable) && column.dataIndex === 'eHeight'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].eHeight"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"

              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


            <!-- 修改数量（卷）-->
            <template v-if="(!props.showDisable) && column.dataIndex === 'qtyJ'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].qtyJ"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handlerTableInnerChange(record,column);
                }"
                @keydown.enter="() => {
                  handlerTableInnerChange(record,column);
                }"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <!-- 汇总信息 -->
        <div class="cs-margin-right cs-list-total-data ">
          数量：{{formatSpecifiedNumber(totalData.qtyTotal,true,2)}} ，金额：{{formatSpecifiedNumber(totalData.totalAmount,true,2)}}，毛重(KG)：{{formatSpecifiedNumber(totalData.grossTotal,true,2)}}，净重(KG)：{{formatSpecifiedNumber(totalData.netTotal,true,2)}}，皮重(KG)：{{formatSpecifiedNumber(totalData.traeTotal,true,2)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>


    <!-- 新增提取合同弹框 -->
    <!-- 新增合同号 -->
    <cs-modal :visible="isShowExtract" title="新增明细" :width="1000" :footer="false" @cancel="handlerBackExtract">
      <template #customContent>

        <div v-if="addLoading"
             style="width: 100%;height: 58vh;display: flex;justify-content: center;align-items: center">
          <a-spin tip="新增中..."/>
        </div>
        <add-export-goods-list-dialog v-else ref="contractModalRef" :parent-id="props.parentId" @cancel="handlerBackExtract"
                                      @save="handlerExtract"></add-export-goods-list-dialog>
      </template>
    </cs-modal>



  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, reactive, ref} from "vue";
import {message, Modal} from "ant-design-vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import {isNullOrEmpty} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import CsModal from "@/components/modal/cs-modal.vue";
import AddExportGoodsListDialog from "@/view/dec/export/componment/AddExportGoodsListDialog.vue";

const  { inputFormatter, inputParser,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()

  /* 引入通用方法 */
  const {
    show,
    tableLoading,
    getTableScroll
  } = useCommon()

  const dataSourceList = ref([])
  const dataVersion = ref(0) // 数据版本号，用于强制组件更新

  defineOptions({
    name: 'BizExportGoodsListList',
  });

  const props = defineProps({
    parentId:{
      type: String,
      required: true,
      default: ''
    },
    isAllConfirmed:{
      type:Boolean,
      default:()=>false
    },
    isEdit:{
      type:Boolean,
      default:()=>false
    },
    /* 是否查看模式 */
    showDisable:{
      type: Boolean,
      default: () => false
    }

  })

  const page = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: '商品描述',
      width: 200,
      align: 'center',
      dataIndex: 'productDesc',
      key: 'productDesc',
    },
    {
      title:'规格',
      width: 200,
      align: 'center',
      dataIndex: 'specification',
      key: 'specification',
      autoHeight: true,
      resizable: true,
    },
    {
      title:'数量（卷）',
      width: 200,
      align: 'center',
      dataIndex: 'qtyJ',
      key: 'qtyJ',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '数量（吨）',
      width: 200,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      customRender: ({ text }) => {
        return cmbShowRender(text,unitList.value)
      }
    },
    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'price',
      key: 'price',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      customRender: ({text}) => {
        return formatSpecifiedNumber(text, true, 2)
      }
    },
    {
      title: '起始箱号',
      width: 200,
      align: 'center',
      dataIndex: 'boxStartNo',
      key: 'boxStartNo',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '结束箱号',
      width: 200,
      align: 'center',
      dataIndex: 'endStartNo',
      key: 'endStartNo',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
    },
    {
      title: '包装样式',
      width: 200,
      align: 'center',
      dataIndex: 'packageStyle',
      key: 'packageStyle',
      customRender: ({ text }) => {
        return cmbShowRender(text,packageList.value)
      }
    },
    {
      title: '毛重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'grossWeight',
      key: 'grossWeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '净重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'netWeight',
      key: 'netWeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '皮重(KG)',
      width: 200,
      align: 'center',
      dataIndex: 'tareWeight',
      key: 'tareWeight',
      customRender: ({text}) => {
        return formatSpecifiedNumber(text, true, 2)
      }
    },
    {
      title: '长(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eLength',
      key: 'eLength',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '宽(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eWidth',
      key: 'eWidth',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '高(M)',
      width: 200,
      align: 'center',
      dataIndex: 'eHeight',
      key: 'eHeight',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    }

  ])




  const tableHeight = ref('')

  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };







  /* 按钮loading */
  const deleteLoading = ref(false)


  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        // deleteLoading.value = true
        // deleteClient(gridData.selectedRowKeys).then(res => {
        //   if (res.code === 200) {
        //     message.success("删除成功！")
        //     getList()
        //   }
        // }).finally(() => {
        //   deleteLoading.value = false
        // })
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizExportGoodsList.delete}/${gridData.selectedRowKeys}`).then(res => {
          if (res.code === 200) {
            // 重置页码到第一页
            page.current = 1
            // 清空选中状态，避免引用已删除的数据
            gridData.selectedData = [];
            gridData.selectedRowKeys = [];
            // 刷新数据
            getList()
            getListSumTotal()
            message.success("删除成功！")
          }else {
            message.error(res.message)
          }
        }).catch((err) => {
          console.error('删除错误',err)
          message.error('删除失败，请重试')
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }

  const getList = async () => {
    try {
      tableLoading.value = true

      // 清空选中状态，避免引用已删除的数据
      gridData.selectedData = [];
      gridData.selectedRowKeys = [];

      const res = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.list}?page=${page.current}&limit=${page.pageSize}`,{parentId:props.parentId})
      if (res.code === 200) {
        // 使用展开运算符确保创建新的数组引用
        dataSourceList.value = [...(res.data || [])]
        page.total = res.total || 0
        // 更新数据版本号，强制组件重新渲染
        dataVersion.value++
      }else {
        message.error(res.message)
        // 出错时也要清空数据，避免显示过期数据
        dataSourceList.value = []
        page.total = 0
        dataVersion.value++
      }
    }catch(err) {
      console.log('错误',err)
      // 异常时清空数据
      dataSourceList.value = []
      page.total = 0
      dataVersion.value++
    }finally {
      tableLoading.value = false
    }

  }

  // 获取表体汇总数据
  const totalData = reactive({
    qtyTotal: 0,
    totalAmount: 0,
    grossTotal: 0,
    netTotal: 0,
    traeTotal: 0
  })

  const getListSumTotal = () => {

    let parentId =  props.parentId
    let params = {
      parentId: parentId
    }
    if (isNullOrEmpty(parentId)) {
      return
    }

    window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.getSummary}/${parentId}`, params).then(res => {
      if (res.code === 200) {
        Object.assign(totalData, res.data);
      } else {
        message.error(res.message)
      }
    }).catch(err => {
      message.error(err.message)
    })
  }



  const onPageChange = (pageNumber, pageSize) =>{
    // console.log('PageNumber:', pageNumber)
    // console.log('PageNumber:', pageSize)
    // console.log('页码或者PageSize发生变化时触发')
    page.current = pageNumber
    page.pageSize = pageSize
    // 在这里添加处理页码变化的逻辑
    getList()
  }





  /* 行内编辑通用方法 */
  const isEditLoading = ref(false)

  /* 处理行内编辑 */
  const handlerTableInnerChange = async (record, column) => {
    if (isEditLoading.value === true) {
      console.log('回车，失焦同时触发！');
      return;
    }

    isEditLoading.value = true;

    // 增强数据有效性检查
    if (!record || !record.id || !column || !column.dataIndex) {
      isEditLoading.value = false;
      return;
    }

    try {
      const res = await window.majesty.httpUtil.getAction(`${ycCsApi.bizExportGoodsList.getListById}/${record.id}`, record);

      if (res.code !== 200 || !res.data) {
        isEditLoading.value = false;
        return;
      }

      const dataTemp = res.data;

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改数量 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      // 如果必要情况下 可以取消 tableLoading
      if (column.dataIndex === 'qty') {
        if (isNullOrEmpty(dataTemp) || dataTemp.qty === record.qty) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNumOrPrice}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.quantity',dataTemp.qty);
          record.qty = dataTemp.qty; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改单价 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'price') {
        if (isNullOrEmpty(dataTemp) || dataTemp.price === record.price) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNumOrPrice}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.price',dataTemp.price);
          record.price = dataTemp.price; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改起始箱号 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'boxStartNo') {
        if (isNullOrEmpty(dataTemp) || dataTemp.boxStartNo === record.boxStartNo) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateStartBoxNo}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.boxStartNo',dataTemp.boxStartNo);
          record.boxStartNo = dataTemp.boxStartNo; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改结束箱号 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'endStartNo') {
        if (isNullOrEmpty(dataTemp) || dataTemp.endStartNo === record.endStartNo) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateEndBoxNo}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.endStartNo',dataTemp.endStartNo);
          record.endStartNo = dataTemp.endStartNo; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改净重 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'netWeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.netWeight === record.netWeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateNetWeight}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.netWeight',dataTemp.netWeight);
          record.netWeight = dataTemp.netWeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改毛重 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'grossWeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.grossWeight === record.grossWeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGrossWeight}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.grossWeight',dataTemp.grossWeight);
          record.grossWeight = dataTemp.grossWeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改长度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eLength') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eLength === record.eLength) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.length',dataTemp.eLength);
          record.eLength = dataTemp.eLength; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改宽度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eWidth') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eWidth === record.eWidth) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.width',dataTemp.eWidth);
          record.eWidth = dataTemp.eWidth; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }

      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改高度 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'eHeight') {
        if (isNullOrEmpty(dataTemp) || dataTemp.eHeight === record.eHeight) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateGoodsLengthHeightWidth}`,record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          // getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.height',dataTemp.eHeight);
          record.eHeight = dataTemp.eHeight; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 修改数量（卷） <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
      if (column.dataIndex === 'qtyJ') {
        if (isNullOrEmpty(dataTemp) || dataTemp.qtyJ === record.qtyJ) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(`${ycCsApi.bizExportGoodsList.updateQtyJ}`, record);
        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
          getListSumTotal()
          // 刷新表头合同金额
          // emitEvent('refresh-3-head-info')
        } else {
          console.log('dataTemp.qtyJ', dataTemp.qtyJ);
          record.qtyJ = dataTemp.qtyJ; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }



    } catch (error) {
      console.error('行内编辑错误:', error);
      message.error(error.message || '操作失败，请重试');
      // 发生错误时重新获取数据，确保数据一致性
      getList();
    } finally {
      // 确保状态重置
      tableLoading.value = false;
      setTimeout(() => {
        isEditLoading.value = false;
      }, 100);
    }
  };



  const unitList = ref([])
  const packageList = ref([])
  const getCommonKeyValueList = async () => {
    try {
      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsHead.getCommonKeyValueList,{});
      if (res.code === 200) {
        unitList.value = res.data.unitList;
        packageList.value = res.data.packageList;
      }else {
        message.error(res.message);
      }
    }catch(err) {
      console.log(err);
    }finally {
    }

  }



  /* 弹框 */
  // 打开提取合同弹框
  // 是否显示新增合同号
  const isShowExtract = ref(false)
  const addLoading = ref(false)
  const handlerSelectContract = () => {
    isShowExtract.value = true
  }

  // 关闭提取合同弹框
  const handlerBackExtract = () => {
    isShowExtract.value = false
  }


  // 保存提取合同
  const handlerExtract = (data) => {
    // console.log('data',data)
    addLoading.value = true
    // 刷新表格数据
    // handlerSearch()
    const params = {
      contractList: data,
      parentId: props.parentId
    }
    window.majesty.httpUtil.postAction(ycCsApi.bizExportGoodsList.extractContractList,params).then((res)=>{
      // console.log('res',res)

      if (res.code !== 200){
        message.error(res.message)

      }else {
        getList()
        getListSumTotal()
        // // 提取完成后，重新刷新数据，
        // Object.assign(formData, res.data.head)
        // editConfig.value.editStatus = editStatus.EDIT
        // editConfig.value.editData =  res.data.head
        // show.value =!show.value;
        // // 通过Bus发送事件，刷新表体数据
        // emitEvent('refreshIncomingGoodsList',res.data.head.id)
        // handlerOnBack({
        //   editData: null,
        //   showBody: true,
        //   editStatus: editStatus.EDIT,
        //   showBodyReceiptSell:false
        // })
      }


    }).finally(()=>{
      addLoading.value = false
      // 关闭提取合同弹框
      isShowExtract.value = false
    })
  }

  // 表单数据
  const formData = reactive({
    // 主键id
    id:'',
    // 业务类型
    businessType:'',
    // 数据状态
    dataState:'',
    // 版本号
    versionNo:'',
    // 企业10位编码
    tradeCode:'',
    // 组织机构代码
    sysOrgCode:'',
    // 父级id
    parentId:'',
    // 创建人
    createBy:'',
    // 创建时间
    createTime:'',
    createTimeTo:'',
    createTimeForm:'',
    // 更新人
    updateBy:'',
    // 更新时间
    updateTime:'',
    updateTimeTo:'',
    updateTimeForm:'',
    // 插入用户名
    insertUserName:'',
    // 更新用户名
    updateUserName:'',
    // 扩展字段1
    extend1:'',
    // 扩展字段2
    extend2:'',
    // 扩展字段3
    extend3:'',
    // 扩展字段4
    extend4:'',
    // 扩展字段5
    extend5:'',
    // 扩展字段6
    extend6:'',
    // 扩展字段7
    extend7:'',
    // 扩展字段8
    extend8:'',
    // 扩展字段9
    extend9:'',
    // 扩展字段10
    extend10:'',
    // 商品名称
    productName:'',
    // 商品描述
    productDesc:'',
    // 数量
    qty:'',
    // 单位
    unit:'',
    // 单价
    price:'',
    // 金额
    amount:'',
    // 起始箱号
    boxStartNo:'',
    // 结束箱号
    endStartNo:'',
    // 包装样式
    packageStyle:'',
    // 毛重(kg)
    grossWeight:'',
    // 净重(kg)
    netWeight:'',
    // 皮重(kg)
    tareWeight:'',
    // 长(m)
    eLongth:'',
    // 宽(m)
    eWidth:'',
    // 高(m)
    eHeight:''
  })

  onMounted(() => {


    // ajaxUrl.selectAllPage = ycCsApi.smoke_machine.list
    // ajaxUrl.exportUrl = ycCsApi.smoke_machine.export

    tableHeight.value = getTableScroll(100,'');

    getList()
    getListSumTotal()
    getCommonKeyValueList()


  })





</script>

<style lang="less" scoped>


</style>
