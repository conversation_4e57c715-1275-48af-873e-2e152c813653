<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

    <!-- 审批状态 -->
    <a-form-item name="apprStatus" v-if="isAudit"   :label="'审批状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.apprStatus" id="apprStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 数据状态 -->
    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 计划编号 -->
    <a-form-item name="planId"   :label="'计划编号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.planId" />
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="signDateFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder="制单日期止"
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'PlanSearch'
})
/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  isAudit: {
    type: Boolean,
    default: false
  }
});
const searchParam = reactive({
  planId: '',
  insertTimeFrom: '',
  insertTimeTo: '',
  status: '',
  apprStatus: '',
  businessType: '1'
})

const locale = {
}
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
