<template>
  <section>
    <a-card size="small" title="进口计划信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('businessType')"
                @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in businessTypes" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 计划编号 -->
          <a-form-item name="planId"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('planId')"
                @click="handleLabelClick('planId')"
              >
                计划编号
              </span>
            </template>
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.planId"/>
          </a-form-item>
          <!-- 计划年度 -->
          <a-form-item name="planYear"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('planYear')"
                @click="handleLabelClick('planYear')"
              >
                计划年度
              </span>
            </template>
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.planYear"
              id="planYear"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY"
              :locale="locale"
              picker="year"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 上下半年 -->
          <a-form-item name="halfYear"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('halfYear')"
                @click="handleLabelClick('halfYear')"
              >
                上下半年
              </span>
            </template>
            <cs-select :disabled="fieldDisabled" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.halfYear" id="halfYear">
              <a-select-option v-for="item in halfYearOptions" :key="item.label"
                               :value="item.value" :label="item.label">
               {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="remark"  class="grid-item merge-3" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('remark')"
                @click="handleLabelClick('remark')"
              >
                备注
              </span>
            </template>
            <a-textarea :disabled="fieldDisabled" size="small" v-model:value="formData.remark" :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="createrUser"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('createrUser')"
                @click="handleLabelClick('createrUser')"
              >
                制单人
              </span>
            </template>
            <a-input disabled size="small" v-model:value="formData.createrUser"/>
          </a-form-item>
          <!-- 制单时间 -->
          <a-form-item name="createrTime"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('createrTime')"
                @click="handleLabelClick('createrTime')"
              >
                制单时间
              </span>
            </template>
            <a-date-picker
              disabled
              v-model:value="formData.createrTime"
              id="createrTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              placeholder=""
              size="small"
              style="width: 100%"
            />
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="status"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('status')"
                @click="handleLabelClick('status')"
              >
                单据状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 确认时间 -->
          <a-form-item name="confirmTime"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('confirmTime')"
                @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker
              disabled
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              showTime
            />
          </a-form-item>
          <!-- 审批状态 -->
          <a-form-item name="apprStatus" :label="'审批状态'" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('apprStatus')"
                @click="handleLabelClick('apprStatus')"
              >
                审批状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.apprStatus" id="apprStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 版本号 -->
          <a-form-item name="versionNo"  class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('versionNo')"
                @click="handleLabelClick('versionNo')"
              >
                版本号
              </span>
            </template>
            <a-input disabled size="small" v-model:value="formData.versionNo"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
              <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                        >
                <template #icon>
                  <GlobalIcon type="cloud" style="color:deepskyblue"/>
                </template>
                审核通过
              </a-button>
              <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                       >
                <template #icon>
                  <GlobalIcon type="close-square" style="color:red"/>
                </template>
                审核退回
              </a-button>
              <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small"  class="cs-card-form">
      <plan-detail-table
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.sid"
        :edit-config="props.editConfig"
        @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}
.form-label:hover {
  opacity: 0.8;
}
.label-green {
  background-color: #52c41a;
  color: white;
}
.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {useFieldMarking} from '@/utils/useFieldMarking';
import PlanDetailTable from './list/PlanDetailTable.vue';
import {getUserInfo} from "@/api/payment/payment_info";
import ycCsApi from "@/api/ycCsApi";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','editShowBody']);

const onBack = (val) => {
  emit('onEditBack', val);
  saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
};

const onBackShowBody = (val,editStatus,headId) => {
  emit('editShowBody', val, editStatus, headId);
};

// 是否禁用
const showDisable = ref(false)
// 添加表头控制字段
const hasHeadControl = computed(() => {
  return (formData.hasHeadCtr === '1' || formData.isCopy ==='1')
})

// 计算最终禁用状态（结合显示模式和表头控制）
const fieldDisabled = computed(() => {
  return showDisable.value || hasHeadControl.value
})
// 添加表头按钮控制字段
const hasHeadButtonControl = computed(() => {
  return (formData.hasHeadCtr === '1')
})
// 计算按钮最终禁用状态（结合显示模式和表头控制）
const buttonDisabled = computed(() => {
  return showDisable.value || hasHeadButtonControl.value
})

const businessTypes = reactive([
  {
    label:'国营贸易进口卷烟',
    value:'1'
  },
  {
    label:'国营贸易进口辅料',
    value:'2'
  },
])

const halfYearOptions = reactive([
  {
    label:'上半年',
    value:'0'
  },
  {
    label:'下半年',
    value:'1'
  },
],)

const portOptions = reactive([
  {
    label:'起运港',
    value:'0'
  },
  {
    label:'目的港',
    value:'1'
  },
])

// 表单数据
const formData = reactive({
  businessType: '1',
  planId: '',
  planYear: '',
  halfYear: '',
  remark: '',
  createrUser: '',
  createrTime: '',
  status: '0',
  confirmTime: '',
  apprStatus: '0',
  versionNo: '1',
  sid:  '',
})

// 表单校验规则
const rules = {
  // businessType: [
  //   {required: true, message: '请选择业务类型', trigger: 'change'},
  //   {max: 60, message: '业务类型不能超过60个字符', trigger: 'blur'}
  // ],
  planId: [
    {required: true, message: '请输入计划编号', trigger: 'blur'},
    {max: 60, message: '计划编号不能超过60个字符', trigger: 'blur'}
  ],
  planYear: [
    {required: true, message: '请选择计划年度', trigger: 'change'},
  ],
  halfYear: [
    {required: true, message: '请选择上下半年', trigger: 'change'},
    {max: 10, message: '上下半年不能超过10个字符', trigger: 'blur'}
  ],
  createrUser: [
    {required: true, message: '请输入制单人', trigger: 'blur'},
  ],
  createrTime: [
    {required: true, message: '请选择制单时间', trigger: 'change'}
  ],
  versionNo: [
    {required: true, message: '请输入版本号', trigger: 'blur'},
  ]
}

// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()

/// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  'businessType': '业务类型',
  'planId': '计划编号',
  'planYear': '计划年度',
  'halfYear': '上下半年',
  'remark': '备注',
  'createrUser': '制单人',
  'createrTime': '制单时间',
  'status': '单据状态',
  'confirmTime': '确认时间',
  'apprStatus': '审批状态',
  'versionNo': '版本号'
}

// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();
const auditLoading = ref(false)
const invalidLoading = ref(false)

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};



const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null&&formData.createrUser===''){
      formData.createrUser =  res.data.userName
    }
  })
}


const pCode = ref('')

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    // 确保从 editConfig 中获取 sid
    if (props.editConfig.editData && props.editConfig.editData.sid) {
      formData.sid = props.editConfig.editData.sid;
    }
    Object.assign(formData, props.editConfig.editData || {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  if (formData.createrTime === '') {
    const now = new Date();
    // 年-月-日
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加1
    const day = String(now.getDate()).padStart(2, '0');
    // 时:分:秒
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    // 组合成目标格式
    formData.createrTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
  getUpdateUser()
  getBySidAndFormType(formData.sid, 'default')

})

/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  // 审核意见输入框
  const auditOpinion = ref('同意审批')
  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'plan',
      }
      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.audit, params)
        .then(res => {
          if (res.code === 200) {
            message.success("审核通过成功！")
            // 返回列表页面
            onBack(true)
          } else {
            message.error(res.message || '审核失败')
          }
        })
        .catch(error => {
          console.error('审核失败:', error)
          message.error('审核失败，请重试')
        })
        .finally(() => {
          auditLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }
  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', {style: 'margin-top: 10px;'}, [
        createVNode('label', {style: 'display: block; margin-bottom: 5px;'}, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => {
            auditOpinion.value = e.target.value
          },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.id],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'plan',
      }
      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.reject, params)
        .then(res => {
          if (res.code === 200) {
            // 审核退回成功后，调用标记保存接口
            return saveCurrentMarkings(formData.id, 'default', fieldMarkings.value)
          } else {
            throw new Error(res.message || '审核退回失败')
          }
        })
        .then(res => {
          message.success("审核退回成功！")
          // 返回列表页面
          onBack(true)
        })
        .catch(error => {
          console.error('审核退回失败:', error)
          message.error(error.message || '审核退回失败，请重试')
        })
        .finally(() => {
          invalidLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}
</script>
