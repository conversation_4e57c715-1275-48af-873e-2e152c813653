<template>
  <a-form ref="formRef" layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }"
          :model="searchParam"
          class="cs-form  grid-container">
    <!-- 出口客户 -->
    <a-form-item name="exportCustomer" :label="'出口客户'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.exportCustomer">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.customerOptions"
                         :key="`${item.label}`" :value="item.value" :label="`${item.label}`">
          {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 出口类别 -->
    <a-form-item name="exportCategory" :label="'出口类别'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.exportCategory">
        <a-select-option class="cs-select-dropdown" v-for="item in optionsConfig.productTypeOptions"
                         :key="`${item.label}`" :value="item.value" :label="`${item.label}`">
          {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref, inject } from 'vue'
import CsSelect from '@/components/select/CsSelect.vue'
import { OPTIONS_CONFIG } from '@/view/annualPlan/js/handle'

const optionsConfig = inject(OPTIONS_CONFIG)

const searchParam = ref({
  exportCustomer: '',
  exportCategory: ''
})

const formRef = ref(null)

const resetSearch = () => {
  formRef.value.resetFields()
}

defineExpose({ searchParam, resetSearch })

defineOptions({
  name: 'ExportAuxMatAnnualPlanSearch'
})
</script>
