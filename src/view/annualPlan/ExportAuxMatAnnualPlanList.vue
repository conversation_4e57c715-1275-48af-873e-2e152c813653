<template>
  <section class="dc-section">
    <div class="cs-action">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh"
                          v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{ localeContent('m.common.button.query') }}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff" />
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff" />
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine" />
          <div ref="area_search">
            <div v-show="showSearch">
              <export-aux-mat-annual-plan-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:annualPlan:add']">
          <a-button :loading="addLoading" size="small" @click="handleAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green" />
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:annualPlan:edit']">
          <a-button size="small" @click="handleEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange" />
            </template>
            {{ localeContent('m.common.button.update') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:annualPlan:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handleDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red" />
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:annualPlan:export']">
          <a-button size="small" :loading="exportLoading" @click="handleExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange" />
            </template>
            {{ localeContent('m.common.button.export') }}
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          column-drag
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight, x: 400 }"
          bordered
          :pagination="false"
          :columns="showColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
        >
          <template #bodyCell="{ column, text, record, index }">
            <div v-if="['planExportAmount'].includes(column.dataIndex)">
              <a-input-number
                v-if="editableData[record.id]"
                v-model:value="editableData[record.id][column.dataIndex]"
                size="small"
                :controls="false"
                :formatter="formatter"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                :precision="column.precision"
                style="width: 100%"
              />
              <span v-else>{{ formatSpecifiedNumber(text, true, 2) }}</span>
            </div>

            <div v-else-if="['exportCustomer', 'exportCategory'].includes(column.dataIndex)">
              <cs-select v-if="editableData[record.id]"
                         optionFilterProp="label" option-label-prop="key" allow-clear
                         show-search v-model:value="editableData[record.id][column.dataIndex]"
                         style="width: 100%">
                <a-select-option class="cs-select-dropdown" v-for="item in selectOptions[column.dataIndex]"
                                 :key="column.withValue ? `${item.value} ${item.label}` : item.label"
                                 :value="item.value"
                                 :label="column.withValue ? `${item.value}${item.label}` : item.label">
                  {{ column.withValue ? `${item.value} ${item.label}` : item.label }}
                </a-select-option>
              </cs-select>

              <span v-else>
                {{
                  getLabel(selectOptions[column.dataIndex], text, column.withValue)
                }}
              </span>
            </div>

            <div v-else-if="['note'].includes(column.dataIndex)">
              <a-input v-if="editableData[record.id]" v-model:value="editableData[record.id][column.dataIndex]"
                       size="small" :maxlength=column.maxLength />
              <span v-else>{{ text }}</span>
            </div>

            <div class="editable-row-operations" v-else-if="column.dataIndex === 'operation'">
              <span v-if="editableData[record.id]">
                <a-typography-link @click="save(record.id)">保存 </a-typography-link>
                <a @click="cancel(record.id)">取消</a>
              </span>
              <span v-else>
                <a @click="edit(record.id)">编辑</a>
              </span>
            </div>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
  </section>
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import BreadCrumb from '@/components/breadcrumb/BreadCrumb.vue'
import ExportAuxMatAnnualPlanSearch from '@/view/annualPlan/ExportAuxMatAnnualPlanSearch.vue'
import CsTableColSettings from '@/components/settings/CsTableColSettings.vue'
import { createVNode, onMounted, ref, toRaw, provide, computed } from 'vue'
import { useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { useCommon } from '@/view/common/useCommon'
import { localeContent } from '@/view/utils/commonUtil'
import { deepClone } from '@/view/utils/common'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import ycCsApi from '@/api/ycCsApi'
import { getColumns } from '@/view/annualPlan/js/columns'
import { getOptionsData, addAnnualPlan, modifyAnnualPlan, removeAnnualPlan } from '@/api/annualPlan'
import { EMPTY } from '@/view/common/constant'
import { OPTIONS_CONFIG } from '@/view/annualPlan/js/handle'

/* 引入通用方法 */
const {
  editConfig,
  getList,
  page,
  showSearch,
  headSearch,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData
} = useCommon()

const { formatSpecifiedNumber } = useColumnsRender()

/**
 * 格式化
 * @param value 值
 */
function formatter(value) {
  if (value === 0) {
    return '0'
  }
  if (!value) {
    return ''
  }
  const parts = value.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}


/**
 * 获取标签
 * @param datasource 数据源
 * @param value 值
 * @param withValue 是否携带值
 */
function getLabel(datasource, value, withValue = true) {
  if (!datasource || !Array.isArray(datasource) || !value) {
    return EMPTY.STRING
  }
  const last = datasource.findLast(item => item && item.value === value)
  if (!last) {
    return EMPTY.STRING
  }
  return Boolean(last.label) ? (withValue ? `${value} ${last.label}` : last.label) : EMPTY.STRING
}

// 配置表格列、导出列
const { tableColumns, excelColumns } = getColumns()

// 原始显示列
const originalColumns = ref((() => {
  for (let columns of tableColumns) {
    columns.visible = true
  }
  return tableColumns
})())

// 当前显示列
const showColumns = ref([...originalColumns.value])

// 自定义显示列更改回调
function customColumnChange(settingColumns) {
  showColumns.value = settingColumns.filter(item => item.visible === true)
}

// 表格唯一key
const tableKey = ref(window['$vueApp'] ? window.majesty.router.currentRoute.value.path : useRoute().path)

// 表格高度
const tableHeight = ref(getTableScroll(100, ''))

// 请求url
ajaxUrl.selectAllPage = ycCsApi.annualPlan.list
ajaxUrl.exportUrl = ycCsApi.annualPlan.export

// 选中行更改回调
function onSelectChange(selectedRowKeys, rowSelectData) {
  gridData.selectedData = rowSelectData
  gridData.selectedRowKeys = selectedRowKeys
}

// 编辑数据
const editableData = ref({})

/**
 * 新增数据
 */
const addLoading = ref(false)

function handleAdd() {
  addLoading.value = true
  try {
    const id = Date.now() + String(Math.floor(Math.random() * 10000)) + '@ADD@'
    const newData = {
      id,
      exportCustomer: '',
      exportCategory: '',
      planExportAmount: undefined,
      note: ''
    }
    const dataList = [...toRaw(dataSourceList.value)]
    dataList.unshift(newData)
    dataSourceList.value = dataList
    editableData.value[id] = deepClone(newData)
  } finally {
    addLoading.value = false
  }
}

/**
 * 编辑数据
 */
function handleEdit() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条要编辑的数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据编辑')
    return
  }
  const id = gridData.selectedRowKeys[0]
  const currentData = toRaw(dataSourceList.value.filter(item => item.id === id)[0])
  editableData.value[id] = deepClone(currentData)
}

/**
 * 删除数据
 */
const deleteLoading = ref(false)

function handleDelete() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择要删除的数据')
    return
  }
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    async onOk() {
      try {
        deleteLoading.value = true
        const res = await removeAnnualPlan(gridData.selectedRowKeys)
        if (res && res.success) {
          message.success('删除成功')
          getList()
        } else {
          message.error(res.message)
        }
      } finally {
        deleteLoading.value = false
      }
    },
    onCancel() {
    }
  })
}

/**
 * 导出数据
 */
function handleExport() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`出口辅料年度计划${timestamp}.xlsx`, ref(excelColumns))
}

/**
 * 行内编辑
 * @param id id
 */
function edit(id) {
  const currentData = dataSourceList.value.filter(item => id === item.id)[0]
  editableData.value[id] = deepClone(toRaw(currentData))
}

/**
 * 行内保存
 * @param id id
 */
async function save(id) {
  const editData = toRaw(editableData.value[id])
  const isAdd = id.endsWith('@ADD@')
  try {
    const res = await (isAdd ? addAnnualPlan(editData) : modifyAnnualPlan(id, editData))
    if (!res.success) {
      message.error(res.message)
      return
    }
    delete editableData.value[id]
    const currentIndex = dataSourceList.value.findIndex(item => item.id === id)
    dataSourceList.value[currentIndex] = res.data
    message.success(isAdd ? '新增成功' : '修改成功')
    getList()
  } finally {

  }
}

/**
 * 行内取消
 * @param id id
 */
function cancel(id) {
  delete editableData.value[id]
  if (id.endsWith('@ADD@')) {
    dataSourceList.value = dataSourceList.value.filter(item => item.id !== id)
  }
}

// 选项配置
const optionsConfig = ref({
  customerOptions: EMPTY.ARRAY,
  productTypeOptions: EMPTY.ARRAY
})

// 选择选项
const selectOptions = computed(() => {
  return {
    exportCustomer: optionsConfig.value.customerOptions,
    exportCategory: optionsConfig.value.productTypeOptions
  }
})

/**
 * 初始化选项配置
 */
async function initOptionsConfig() {
  const res = await getOptionsData()
  if (!res.success) {
    message.warn('选项配置初始化失败')
    return
  }
  const data = res.data
  optionsConfig.value.customerOptions = data.customerOptions
  optionsConfig.value.productTypeOptions = data.productTypeOptions
}

provide(OPTIONS_CONFIG, optionsConfig)

onMounted(() => {
  getList()
  initOptionsConfig()
})

defineOptions({
  name: 'ExportAuxMatAnnualPlanList'
})
</script>
