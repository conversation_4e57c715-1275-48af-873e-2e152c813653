import { baseColumns } from '@/view/common/baseColumns'

const { baseColumnsExport, baseColumnsShow } = baseColumns()

export function getColumns() {
  const commColumns = [
    'id',
    'exportCustomer',
    'exportCategory',
    'planExportAmount',
    'note'
  ]

  // 导出字段设置
  const excelColumnsConfig = [
    ...commColumns,
    ...baseColumnsExport
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  // table表格字段设置
  const totalColumns = [
    {
      width: 100,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left'
    },
    {
      title: '出口客户',
      minWidth: 200,
      align: 'center',
      dataIndex: 'exportCustomer',
      resizable: true,
      key: 'exportCustomer',
      withValue: false
    },
    {
      title: '出口品类',
      minWidth: 200,
      align: 'center',
      dataIndex: 'exportCategory',
      resizable: true,
      key: 'exportCategory',
      withValue: false
    },
    {
      title: '年计划出口总量（万支，吨）',
      minWidth: 180,
      align: 'center',
      dataIndex: 'planExportAmount',
      resizable: true,
      key: 'planExportAmount',
      precision: 6
    },
    {
      title: '备注',
      minWidth: 200,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
      maxLength: 200
    }
  ]

  return {
    tableColumns: totalColumns.filter(item => columnsConfig.includes(item.key)),
    excelColumns: totalColumns.filter(item => excelColumnsConfig.includes(item.key))
  }
}
