<template>
  <section>
    <!-- 表头部分 -->
    <a-card size="small" title="表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">

          <!-- 月份字段 -->
          <a-form-item name="month" :label="'月份'" class="grid-item" :colon="false">
            <a-input
              v-model:value="formData.monthDisplay"
              size="small"
              disabled
              placeholder="月份"
            />
          </a-form-item>

          <!-- 操作按钮 -->
          <div class="cs-submit-btn merge-3">
<!--            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>

    <!-- 表体部分 -->
    <a-card size="small" title="表体" class="cs-card-form">
      <div class="cs-action-btn" style="margin-bottom: 16px;">
        <div class="cs-action-btn-item">
          <a-button size="small" @click="handlerAddRow">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增
          </a-button>
        </div>
        <div class="cs-action-btn-item">
          <a-button size="small" @click="handlerDeleteRow">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            删除
          </a-button>
        </div>
      </div>

      <!-- 表格 -->
      <s-table
        :animate-rows="false"
        ref="tableRef"
        class="cs-action-item"
        size="small"
        :scroll="{ y: 400, x: 400 }"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :columns="tableColumns"
        :data-source="dataSourceList"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        row-key="sid"
      >
        <template #bodyCell="{ column, text, record }">
          <!-- 币种列 -->
          <template v-if="column.dataIndex === 'curr'">
            <div>
              <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="请选择币种"
                  :options="currList"
                ></a-select>
              </span>
              <span v-else>
                {{ formatCurrency(record.curr) }}
              </span>
            </div>
          </template>

          <!-- 汇率列 -->
          <template v-if="column.dataIndex === 'rate'">
            <div>
              <span v-if="editableData[record.sid]">
                <a-input-number
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%; margin: -5px 0"
                  :precision="6"
                  :max="9999999999999"
                  :min="0"
                  placeholder="请输入汇率"
                />
              </span>
              <span v-else>
                {{ formatNumber(text, 6) }}
              </span>
            </div>
          </template>

          <!-- 美元汇率列 -->
          <template v-if="column.dataIndex === 'usdRate'">
            <div>
              <span v-if="editableData[record.sid]">
                <a-input-number
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%; margin: -5px 0"
                  :precision="6"
                  :max="9999999999999"
                  :min="0"
                  placeholder="请输入美元汇率"
                />
              </span>
              <span v-else>
                {{ formatNumber(text, 6) }}
              </span>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.dataIndex === 'operation'">
            <div class="editable-row-operations">
              <span v-if="editableData[record.sid]">
                <a-typography-link @click="save(record.sid)">保存</a-typography-link>
                <a @click="cancel(record.sid)">取消</a>
              </span>
              <span v-else>
                <a @click="edit(record.sid)">编辑</a>
              </span>
            </div>
          </template>
        </template>
      </s-table>
    </a-card>
  </section>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { editStatus } from '@/view/common/constant'
import { usePCode } from '@/view/common/usePCode'
import { deepClone } from '@/view/utils/common'
import { useCommon } from '@/view/common/useCommon'
import ycCsApi from "@/api/ycCsApi"
import {
  insertEnterpriseRateTable,
  updateEnterpriseRateTable,
} from "@/api/params/params_info"

const { getPCode } = usePCode()

// 使用通用方法
const {
  page,
  dataSourceList,
  tableLoading,
  getList,
  ajaxUrl
} = useCommon()

// 组件注册
defineOptions({
  name: 'RateTableEdit',
  components: {
  }
})

// Props
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['on-back'])

// Refs
const formRef = ref(null)
const tableRef = ref(null)

// Reactive data
const formData = reactive({
  month: '', // 存储YYYYMM格式的月份
  monthDisplay: '' // 用于显示的月份格式
})

const editableData = reactive({})
const selectedRowKeys = ref([])
const currList = ref([])
const pCode = ref('')
const showDisable = ref(false)

// 表格列定义
const tableColumns = ref([
  {
    width: 80,
    minWidth: 80,
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    fixed: 'left',
  },
  {
    title: '币种',
    width: 220,
    align: 'center',
    dataIndex: 'curr',
    key: 'curr',
  },
  {
    title: '汇率',
    width: 220,
    align: 'center',
    dataIndex: 'rate',
    key: 'rate',
  },
  {
    title: '美元汇率',
    width: 220,
    align: 'center',
    dataIndex: 'usdRate',
    key: 'usdRate',
  }
])

// 表单验证规则
const rules = {
  month: [
    { required: true, message: '请选择月份', trigger: 'change' }
  ]
}

// 格式化币种显示
const formatCurrency = (code) => {
  const option = currList.value.find(opt => opt.value === code)
  return option ? option.label : code
}

// 格式化数字显示
const formatNumber = (value, precision = 6) => {
  if (value === null || value === undefined || value === '') {
    return ''
  }

  const num = parseFloat(value)
  if (isNaN(num)) {
    return value
  }

  // 根据用户偏好，为整数添加.00，保留非整数的小数位
  if (Number.isInteger(num)) {
    return num.toFixed(2)
  } else {
    return num.toFixed(precision)
  }
}

// 选中事件
const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys
}

// 设置月份显示格式
const setMonthDisplay = (monthValue) => {
  if (monthValue && monthValue.length === 6) {
    const year = monthValue.substring(0, 4)
    const month = monthValue.substring(4, 6)
    formData.monthDisplay = `${year}-${month}`
  } else {
    formData.monthDisplay = ''
  }
}

// 加载表格数据 - 包含月份查询条件
const loadTableData = () => {
  if (!formData.month) {
    return
  }

  // 直接调用doSearch，因为useCommon中的getList实际上就是doSearch
  tableLoading.value = true
  const searchParams = {
    month: formData.month
  }

  window.majesty.httpUtil.postAction(`${ajaxUrl.selectAllPage}?page=${page.current}&limit=${page.pageSize}`,
    searchParams
  ).then(res => {
    dataSourceList.value = res.data
    page.total = res.total
  }).finally(() => {
    tableLoading.value = false
  })
}

// 新增行
const handlerAddRow = () => {
  const sid = Date.now() + "add"
  const newData = {
    sid: `${sid}`,
    month: formData.month, // 从表头获取月份
    curr: "",
    rate: null,
    usdRate: null
  }

  // 在异步回调中更新数据源
  dataSourceList.value.unshift(newData)
  dataSourceList.value = [...dataSourceList.value]
  editableData[sid] = deepClone(newData)
}

// 删除行
const handlerDeleteRow = () => {
  if (selectedRowKeys.value.length <= 0) {
    message.warning('请选择要删除的行')
    return
  }

  // 从表格数据中移除选中的行
  dataSourceList.value = dataSourceList.value.filter(item => !selectedRowKeys.value.includes(item.sid))

  // 清理编辑数据
  selectedRowKeys.value.forEach(sid => {
    delete editableData[sid]
  })

  selectedRowKeys.value = []
  message.success('删除成功')
}

// 编辑行
const edit = (sid) => {
  editableData[sid] = deepClone(dataSourceList.value.find(item => item.sid === sid))
}

// 保存行 - 使用与List.vue相同的逻辑
const save = (sid) => {
  // 1. 找到数据源中与当前 sid 匹配的项
  const targetItem = dataSourceList.value.find(item => item.sid === sid)

  // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
  Object.assign(targetItem, editableData[sid])

  // 确保月份从表头获取
  targetItem.month = formData.month

  if (targetItem.curr == null || targetItem.curr === "" || targetItem.curr === undefined) {
    message.error('币种不能为空！')
    return
  }

  // 验证汇率数值
  if (targetItem.rate !== null && targetItem.rate !== undefined && targetItem.rate !== '') {
    const rateNum = parseFloat(targetItem.rate)
    if (isNaN(rateNum) || rateNum < 0 || rateNum > 9999999999999) {
      message.error('汇率必须是0-9999999999999之间的数值！')
      return
    }
  }

  // 验证美元汇率数值
  if (targetItem.usdRate !== null && targetItem.usdRate !== undefined && targetItem.usdRate !== '') {
    const usdRateNum = parseFloat(targetItem.usdRate)
    if (isNaN(usdRateNum) || usdRateNum < 0 || usdRateNum > 9999999999999) {
      message.error('美元汇率必须是0-9999999999999之间的数值！')
      return
    }
  }

  // 3. 删除临时编辑的数据，清理内存
  delete editableData[sid]

  if (sid.includes("add")) {
    // 执行新增请求
    insertEnterpriseRateTable(targetItem).then((res) => {
      if (res.code === 200) {
        message.success('新增成功!')
      } else {
        message.error(res.message)
      }
    }).finally(() => {
      loadTableData() // 重新加载表格数据
    })
  } else {
    // 执行更新请求
    updateEnterpriseRateTable(sid, targetItem).then((res) => {
      if (res.code === 200) {
        message.success('修改成功!')
      } else {
        message.error(res.message)
      }
    }).finally(() => {
      loadTableData() // 重新加载表格数据
    })
  }
}

// 取消编辑 - 使用与List.vue相同的逻辑
const cancel = (sid) => {
  delete editableData[sid]
  loadTableData() // 重新加载表格数据
}

// 保存整个表单 - 简化版本，主要用于验证
const handlerSave = async () => {
  try {
    await formRef.value.validate()

    if (dataSourceList.value.length === 0) {
      message.error('请至少添加一行汇率数据')
      return
    }

    // 检查是否有未保存的编辑行
    const hasUnsavedRows = Object.keys(editableData).length > 0
    if (hasUnsavedRows) {
      message.error('请先保存所有编辑中的行')
      return
    }

    message.success('所有数据已保存！')
    onBack(true)

  } catch (error) {
    console.error('验证失败:', error)
    message.error('请检查表单数据')
  }
}

// 返回
const onBack = (refresh = false) => {
  emit('on-back', refresh)
}

// 初始化
onMounted(() => {
  // 设置API URL
  ajaxUrl.selectAllPage = ycCsApi.params.enterpriseRate.list

  // 获取币种数据
  getPCode().then(res => {
    pCode.value = res
    currList.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }))
  })

  // 设置默认月份为当前月份
  if (props.editConfig.editStatus === editStatus.ADD) {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0')
    formData.month = `${currentYear}${currentMonth}`

    // 设置显示格式
    setMonthDisplay(formData.month)

    // 加载该月份的数据
    loadTableData()
  }

  // 如果是编辑模式，设置月份
  if (props.editConfig.editStatus === editStatus.EDIT && props.editConfig.editData && props.editConfig.editData.month) {
    formData.month = props.editConfig.editData.month
    setMonthDisplay(formData.month)

    // 加载该月份的数据
    loadTableData()
  }

  // 根据编辑状态设置禁用状态
  if (props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
})
</script>

<style lang="less" scoped>
.editable-row-operations a {
  margin-right: 8px;
}
</style>
