<template>
  <section>
    <a-card size="small" title="表头" class="cs-card-form head">
      <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="formRules"
              :model="formData" class="grid-container cs-form">
        <!-- 业务类型 -->
        <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
          <cs-select disabled allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.businessType">
            <a-select-option v-for="item in productClassify.commonBusinessType" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 合同号 -->
        <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
          <a-input :disabled="!addFlag" size="small" v-model:value="formData.contractNo" maxlength="60"/>
        </a-form-item>

        <!-- 业务地点 -->
        <a-form-item name="businessPlace" :label="'业务地点'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.businessPlace">
            <a-select-option v-for="item in productClassify.business_location" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 买方 -->
        <a-form-item name="buyer" :label="'买方'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.buyer">
            <a-select-option v-for="item in optionsConfig.customerOptions" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 卖方 -->
        <a-form-item name="seller" :label="'卖方'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.seller">
            <a-select-option v-for="item in optionsConfig.supplierOptions" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 使用厂家 -->
        <a-form-item name="usingManufacturer" :label="'使用厂家'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.usingManufacturer">
            <a-select-option v-for="item in optionsConfig.customerOptions" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 国内委托方 -->
        <a-form-item name="domesticClient" :label="'国内委托方'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.domesticClient">
            <a-select-option v-for="item in optionsConfig.customerOptions" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 签约日期 -->
        <a-form-item name="signDate" :label="'签约日期'" class="grid-item" :colon="false">
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.signDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 签约地点(中文) -->
        <a-form-item name="signPlaceCn" :label="'签约地点(中文)'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.signPlaceCn" @select="handleSignPlaceSelect">
            <a-select-option v-for="item in optionsConfig.cityOptions"
                             :key="item.label" :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 签约地点(英文) -->
        <a-form-item name="signPlaceEn" :label="'签约地点(英文)'" class="grid-item" :colon="false">
          <a-input :disabled="showFlag" size="small" v-model:value="formData.signPlaceEn" maxlength="100" />
        </a-form-item>

        <!-- 合同生效期 -->
        <a-form-item name="contractEffectiveDate" :label="'合同生效期'" class="grid-item" :colon="false">
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.contractEffectiveDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 合同有效期 -->
        <a-form-item name="contractValidityDate" :label="'合同有效期'" class="grid-item" :colon="false">
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.contractValidityDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 运输方式 -->
        <a-form-item name="transportMode" :label="'运输方式'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.transportMode">
            <a-select-option v-for="item in productClassify.transportMode" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 装运港 -->
        <a-form-item name="shippingPort" :label="'装运港'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.shippingPort">
            <a-select-option v-for="item in optionsConfig.portOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 目的港 -->
        <a-form-item name="destPort" :label="'目的港'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.destPort">
            <a-select-option v-for="item in optionsConfig.portOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 报关口岸 -->
        <a-form-item name="customsDeclarationPort" :label="'报关口岸'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.customsDeclarationPort">
            <a-select-option v-for="item in optionsConfig.portOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 付款方式 -->
        <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.paymentMethod">
            <a-select-option v-for="item in productClassify.paymentMethod" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 币种 -->
        <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.curr">
            <a-select-option v-for="item in optionsConfig.currOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 价格条款 -->
        <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.priceTerm">
            <a-select-option v-for="item in optionsConfig.priceTermsOptions" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 价格条款对应港口 -->
        <a-form-item name="priceTermPort" :label="'价格条款对应港口'" class="grid-item" :colon="false">
          <cs-select :disabled="showFlag" allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.priceTermPort">
            <a-select-option v-for="item in productClassify.priceTermPort" :key="item.label"
                             :value="item.value" :label="item.label">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 建议授权签约人 -->
        <a-form-item name="suggestAuthorSignatory" :label="'建议授权签约人'" class="grid-item" :colon="false">
          <a-input :disabled="showFlag" size="small" v-model:value="formData.suggestAuthorSignatory" maxlength="50" />
        </a-form-item>

        <!-- 短溢数 -->
        <a-form-item name="shortOverflowNumber" :label="'短溢数'" class="grid-item short-number" :colon="false">
          <a-input-number :disabled="showFlag" size="small" v-model:value="formData.shortOverflowNumber"
                          :addon-after="' % '" style="width: 100%" :precision="4" :controls="false" />
        </a-form-item>

        <!-- 备注 -->
        <a-form-item name="note" :label="'备注'" class="grid-item merge-3" :colon="false">
          <a-textarea :disabled="showFlag" size="small" v-model:value="formData.note" maxlength="200"
                      :auto-size="{ minRows: 2, maxRows:  3}" />
        </a-form-item>

        <!-- 制单人 -->
        <a-form-item name="documentMaker" :label="'制单人'" class="grid-item" :colon="false">
          <a-input disabled size="small" v-model:value="formData.documentMaker" />
        </a-form-item>

        <!-- 制单日期 -->
        <a-form-item name="documentMakeDate" :label="'制单日期'" class="grid-item" :colon="false">
          <a-date-picker disabled v-model:value="formData.documentMakeDate" :valueFormat="DATE_FORMAT.DATE_TIME"
                         :format="DATE_FORMAT.DATE_TIME"
                         :locale="zhCN" :placeholder="EMPTY.STRING" size="small" style="width: 100%" />
        </a-form-item>

        <!-- 单据状态 -->
        <a-form-item name="dataStatus" :label="'单据状态'" class="grid-item" :colon="false">
          <cs-select disabled allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.dataStatus">
            <a-select-option v-for="item in productClassify.data_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 确认时间 -->
        <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
          <a-date-picker
            disabled
            v-model:value="formData.confirmTime"
            :valueFormat="DATE_FORMAT.DATE_TIME"
            :format="DATE_FORMAT.DATE_TIME"
            :locale="zhCN"
            placeholder=""
            size="small"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 审批状态 -->
        <a-form-item name="apprStatus" :label="'审批状态'" class="grid-item" :colon="false">
          <cs-select disabled allow-clear show-search optionFilterProp="label" option-label-prop="key"
                     v-model:value="formData.apprStatus">
            <a-select-option v-for="item in productClassify.approval_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 版本号 -->
        <a-form-item name="versionNo" :label="'版本号'" class="grid-item" :colon="false">
          <a-input disabled size="small" v-model:value="formData.versionNo" />
        </a-form-item>

        <div class="cs-submit-btn merge-3">
          <a-button v-if="!showFlag" size="small" type="primary" @click="handleSave" class="cs-margin-right"
                    :loading="saveLoading">
            保存
          </a-button>
          <a-button size="small" @click="edit.back(true)">
            返回
          </a-button>
          <a-button v-if="!showFlag && !addFlag && formData.dataStatus === DATA_STATUS.DRAFT" type="text"
                    size="small" :icon="h(CheckOutlined)" @click="handleConfirm">
            确认
          </a-button>
        </div>
      </a-form>
    </a-card>

    <a-card title="表体" size="small" class="cs-card-form body">
      <foreign-contract-body-list ref="bodyListRef" />
    </a-card>
  </section>
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import ForeignContractBodyList from '@/view/equipment/foreignContract/body/ForeignContractBodyList.vue'
import { ref, inject, onMounted, h, createVNode, watch, provide } from 'vue'
import CheckOutlined from '@ant-design/icons-vue/lib/icons/CheckOutlined'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { message, Modal } from 'ant-design-vue'
import { productClassify, DATE_FORMAT, DATA_STATUS, EMPTY } from '@/view/common/constant'
import { insertContract, updateContract, confirmContract, getDollarRate } from '@/api/equipment/foreignContract'
import { EDIT, OPTIONS_CONFIG, DOLLAR_RATE } from '@/view/equipment/foreignContract/js/handle'

const edit = inject(EDIT)
const optionsConfig = inject(OPTIONS_CONFIG)

// 通用显示标识
const showFlag = edit.commonShowFlag
// 通用新增标识
const addFlag = edit.commonAddFlag

// 表头form ref
const formRef = ref(null)

// 表体ref
const bodyListRef = ref(null)

// 美元汇率
const dollarRate = ref(1)

// 表头form数据
const formData = ref({
  businessType: '',
  contractNo: '',
  businessPlace: '',
  buyer: '',
  seller: '',
  usingManufacturer: '',
  domesticClient: '',
  signDate: '',
  signPlaceCn: '',
  signPlaceEn: '',
  contractEffectiveDate: undefined,
  contractValidityDate: undefined,
  transportMode: '',
  shippingPort: '',
  destPort: '',
  customsDeclarationPort: '',
  paymentMethod: '',
  curr: '',
  priceTerm: '',
  priceTermPort: '',
  suggestAuthorSignatory: '',
  shortOverflowNumber: '',
  note: '',
  versionNo: '',
  documentMaker: '',
  documentMakeDate: undefined,
  dataStatus: '',
  confirmTime: undefined,
  apprStatus: ''
})

// 表头form校验规则
const formRules = {
  businessType: [
    { required: true, message: '请输入业务类型', trigger: 'blur' }
  ],
  contractNo: [
    { required: true, message: '请输入合同号', trigger: 'blur' }
  ],
  businessPlace: [
    { required: true, message: '请选择业务地点', trigger: 'blur' }
  ],
  buyer: [
    { required: true, message: '请选择买方', trigger: 'change' }
  ],
  seller: [
    { required: true, message: '请选择卖方', trigger: 'change' }
  ],
  curr: [
    { required: true, message: '请选择币种', trigger: 'change' }
  ],
  versionNo: [
    { required: true, message: '请输入版本号', trigger: 'change' }
  ],
  dataStatus: [
    { required: true, message: '请选择数据状态', trigger: 'change' }
  ]
}

/**
 * 处理签约地点选择
 */
function handleSignPlaceSelect(cityCnName) {
  if (!cityCnName) {
    return
  }
  const currentOption = optionsConfig.value.cityOptions.filter(city => city.value === cityCnName)
  if (currentOption && currentOption.length && currentOption.length > 0) {
    formData.value.signPlaceEn = currentOption[0].enName
  }
}

// 获取美元汇率
async function setDollarRate(curr) {
  try {
    if (!curr || String(curr).trim() === '') {
      dollarRate.value = undefined
      return
    }
    if (curr === 'USD') {
      dollarRate.value = 1
      return
    }
    const res = await getDollarRate(curr)
    if (!res.success) {
      message.error(res.message)
      dollarRate.value = undefined
      return
    }
    dollarRate.value = res.data
  } finally {
    bodyListRef.value.flushDollarRate()
  }
}

watch(() => formData.value.curr, curr => setDollarRate(curr))

const saveLoading = ref(false)

/**
 * 保存
 */
async function handleSave() {
  saveLoading.value = true
  try {
    await formRef.value.validate()
    const params = addFlag.value ? { head: { ...formData.value } } : formData.value
    if (addFlag.value) {
      params.bodyList = bodyListRef.value.getAddList()
    }
    const res = await (addFlag.value ? insertContract(params) : updateContract(edit.config.value['headId'], params))
    if (!res.success) {
      message.error(res.message)
      return
    }
    message.success(addFlag.value ? '新增成功' : '修改成功')
    // 新增状态更改为修改状态
    if (addFlag.value) {
      edit.toEdit(res.data)
    }
    formData.value = res.data
    bodyListRef.value.getList()
  } catch (e) {
    console.log('save error', e)
  } finally {
    saveLoading.value = false
  }
}

/**
 * 确认
 */
async function handleConfirm() {
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否确认所选项？',
    onOk: async () => {
      const headId = edit.config.value['headId']
      const updateRes = await updateContract(headId, formData.value)
      if (!updateRes || !updateRes.success) {
        message.error('确认触发保存失败')
        return
      }
      formData.value = updateRes.data
      const confirmRes = await confirmContract(headId)
      if (!confirmRes || !confirmRes.success) {
        message.error(confirmRes.message)
        return
      }
      const { confirmTime, dataStatus } = confirmRes.data
      formData.value.confirmTime = confirmTime
      formData.value.dataStatus = dataStatus
      bodyListRef.value.toReadonly()
      message.success('确认成功')
    }
  })
}

/**
 * 初始化表单数据
 */
function initFormData() {
  if (addFlag.value) {
    formData.value.businessType = '3'
    formData.value.businessPlace = '0'
    formData.value.transportMode = '0'
    formData.value.customsDeclarationPort = 'CHN331'
    formData.value.curr = 'USD'
    formData.value.priceTermPort = '1'
    formData.value.versionNo = '1'
    formData.value.dataStatus = DATA_STATUS.DRAFT
    formData.value.apprStatus = '0'
    return
  }
  const editConfig = edit.config.value
  formData.value = Object.assign(formData.value, editConfig['editData'])
}

// 美元汇率
provide(DOLLAR_RATE, dollarRate)

onMounted(() => {
  initFormData()
})

defineOptions({
  name: 'EquipmentForeignContractEdit'
})
</script>

<style scoped>
:deep(.cs-submit-btn) {
  padding-bottom: 0;
  margin: 10px 0
}

.head :deep(.ant-card-body) {
  padding-bottom: 0;
}

.body :deep(.ant-card-body) {
  padding-top: 0;
}

:deep(.short-number .ant-input-number-input-wrap input) {
  font-size: 12px;
}
</style>
