import { Tag } from 'ant-design-vue'
import { baseColumns } from '@/view/common/baseColumns'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { productClassify, DATA_STATUS, APPROVAL_STATUS } from '@/view/common/constant'

const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()

export function getHeadColumns() {
  const commColumns = [
    'id',
    'businessType',
    'contractNo',
    'buyer',
    'seller',
    'usingManufacturer',
    'domesticClient',
    'contractValidityDate',
    'documentMaker',
    'documentMakeDate',
    'dataStatus',
    'confirmTime'
  ]

  // 导出字段设置
  const excelColumnsConfig = [
    ...commColumns,
    ...baseColumnsExport
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  const headColumns = [
    {
      minWidth: 100,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left'
    },
    {
      minWidth: 140,
      title: '业务类型',
      align: 'center',
      dataIndex: 'businessType',
      resizable: true,
      key: 'businessType',
      customRender: ({ text }) => {
        return (<span>{cmbShowRender(text, productClassify.businessType)}</span>)
      }
    },
    {
      minWidth: 200,
      title: '合同号',
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo'
    },
    {
      minWidth: 200,
      title: '买方',
      align: 'center',
      dataIndex: 'buyer',
      resizable: true,
      key: 'buyer'
    },
    {
      minWidth: 200,
      title: '卖方',
      align: 'center',
      dataIndex: 'seller',
      resizable: true,
      key: 'seller'
    },
    {
      minWidth: 200,
      title: '使用厂家',
      align: 'center',
      dataIndex: 'usingManufacturer',
      resizable: true,
      key: 'usingManufacturer'
    },
    {
      minWidth: 200,
      title: '国内委托方',
      align: 'center',
      dataIndex: 'domesticClient',
      resizable: true,
      key: 'domesticClient'
    },
    {
      minWidth: 100,
      title: '合同有效期',
      align: 'center',
      dataIndex: 'contractValidityDate',
      resizable: true,
      key: 'contractValidityDate'
    },
    {
      title: '制单人',
      minWidth: 100,
      align: 'center',
      dataIndex: 'documentMaker',
      resizable: true,
      key: 'documentMaker'
    },
    {
      minWidth: 153,
      title: '制单时间',
      align: 'center',
      dataIndex: 'documentMakeDate',
      resizable: true,
      key: 'documentMakeDate'
    },
    {
      minWidth: 100,
      title: '单据状态',
      align: 'center',
      dataIndex: 'dataStatus',
      resizable: true,
      key: 'dataStatus',
      customRender: ({ text }) => {
        const color = text === DATA_STATUS.DRAFT ? 'success' : (text === DATA_STATUS.CONFIRMED ? 'processing' : 'error')
        return (<Tag color={color}>{cmbShowRender(text, productClassify.data_status)}</Tag>)
      }
    },
    {
      minWidth: 153,
      title: '确认时间',
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime'
    }
  ]

  return {
    tableColumns: headColumns.filter(item => columnsConfig.includes(item.key)),
    excelColumns: headColumns.filter(item => excelColumnsConfig.includes(item.key))
  }
}

export function getBodyColumns() {
  const columnsConfig = [
    ...baseColumnsShow,
    'id',
    'gName',
    'gModel',
    'qty',
    'unit',
    'unitPrice',
    'moneyAmount',
    'deliveryDate',
    'convertedTotalDollars',
    'note'
  ]

  const bodyColumns = [
    {
      minWidth: 180,
      title: '商品名称',
      align: 'center',
      dataIndex: 'gName',
      resizable: true,
      key: 'gName'
    },
    {
      minWidth: 160,
      title: '产品型号',
      align: 'center',
      dataIndex: 'gModel',
      resizable: true,
      key: 'gModel'
    },
    {
      minWidth: 120,
      title: '数量',
      align: 'center',
      dataIndex: 'qty',
      resizable: true,
      key: 'qty',
      precision: 4
    },
    {
      minWidth: 110,
      title: '单位',
      align: 'center',
      dataIndex: 'unit',
      resizable: true,
      key: 'unit',
      withValue: true
    },
    {
      minWidth: 110,
      title: '单价',
      align: 'center',
      dataIndex: 'unitPrice',
      resizable: true,
      key: 'unitPrice',
      precision: 8
    },
    {
      minWidth: 120,
      title: '金额',
      align: 'center',
      dataIndex: 'moneyAmount',
      resizable: true,
      key: 'moneyAmount'
    },
    {
      minWidth: 155,
      title: '交货日期',
      align: 'center',
      dataIndex: 'deliveryDate',
      resizable: true,
      key: 'deliveryDate'
    },
    {
      minWidth: 120,
      title: '总价折美元',
      align: 'center',
      dataIndex: 'convertedTotalDollars',
      resizable: true,
      key: 'convertedTotalDollars'
    },
    {
      minWidth: 150,
      title: '备注',
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note'
    }
  ]

  return bodyColumns.filter(item => columnsConfig.includes(item.key))
}

export function getMaterialColumns() {
  const columnsConfig = [
    ...baseColumnsShow,
    'id',
    'gName',
    'fullEnName',
    'merchandiseCategories',
    'supplier'
  ]

  const materialColumns = [
    {
      width: '25%',
      title: '商品名称',
      align: 'center',
      dataIndex: 'gName',
      resizable: true,
      key: 'gName'
    },
    {
      width: '25%',
      title: '英文名称',
      align: 'center',
      dataIndex: 'fullEnName',
      resizable: true,
      key: 'fullEnName'
    },
    {
      width: '15%',
      title: '商品类别',
      align: 'center',
      dataIndex: 'merchandiseCategories',
      resizable: true,
      key: 'merchandiseCategories'
    },
    {
      width: '35%',
      title: '供应商',
      align: 'center',
      dataIndex: 'supplier',
      resizable: true,
      key: 'supplier'
    }
  ]

  return materialColumns.filter(item => columnsConfig.includes(item.key))
}

export function getAuditColumns() {
  const { tableColumns: columns } = getHeadColumns()
  const operationIndex = columns.findIndex(item => item.dataIndex === 'operation')
  const approvalColumn = {
    minWidth: 100,
    title: '审核状态',
    align: 'center',
    dataIndex: 'apprStatus',
    key: 'apprStatus',
    resizable: true,
    customRender: ({ text }) => {
      let color
      switch (text) {
        case APPROVAL_STATUS.PASS: {
          color = 'success'
          break
        }
        case APPROVAL_STATUS.RETURN: {
          color = 'error'
          break
        }
        case APPROVAL_STATUS.UNAPPROVED: {
          color = 'warning'
          break
        }
        case APPROVAL_STATUS.PROCESSING: {
          color = 'processing'
          break
        }
        default: {
          color = 'default'
        }
      }
      return (<Tag color={color}>{cmbShowRender(text, productClassify.approval_status)}</Tag>)
    }
  }
  columns.splice(operationIndex + 1, 0, approvalColumn)
  return columns
}
