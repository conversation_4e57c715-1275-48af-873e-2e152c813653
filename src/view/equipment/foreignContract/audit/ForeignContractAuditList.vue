<template>
  <section class="dc-section" ref="dcSectionRef">
    <div v-show="show" class="cs-action">
      <!-- 查询区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button v-show="showSearch" class="cs-margin-right cs-refresh"
                          size="small" type="primary" @click="handlerRefresh">
                  <template #icon>
                    <global-icon type="redo" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{ localeContent('m.common.button.query') }}
                  <template #icon>
                    <global-icon type="search" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <global-icon v-show="!showSearch" type="down" style="color:#fff" />
                    <global-icon v-show="showSearch" type="up" style="color:#fff" />
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine" />
          <div ref="area_search">
            <div v-show="showSearch">
              <foreign-contract-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:equipment-foreignContract:pass']">
          <a-button size="small" :loading="passLoading" @click="handlePass">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue" />
            </template>
            审核通过
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:equipment-foreignContract:return']">
          <a-button size="small" :loading="returnLoading" @click="handleReturn">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red" />
            </template>
            审核退回
          </a-button>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <cs-table-col-settings
            :resId="tableKey"
            :tableKey="tableKey + '-equipmentForeignContract'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </cs-table-col-settings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          bordered
          column-drag
          :pagination="false"
          :columns="showColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :style="listTableStyle"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, record, text}">
            <template v-if="column.dataIndex === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                >
                  <template #icon>
                    <global-icon type="search" style="color:#1677ff" />
                  </template>
                </a-button>
              </div>
            </template>
            <template v-else-if="['buyer', 'usingManufacturer', 'domesticClient'].includes(column.dataIndex)">
              <span>{{ cmbShowRender(text, optionsConfig.customerOptions) }}</span>
            </template>
            <template v-else-if="['seller'].includes(column.dataIndex)">
              <span>{{ cmbShowRender(text, optionsConfig.supplierOptions) }}</span>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页区域 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- tabs -->
    <foreign-contract-tabs v-if="!show" />
  </section>
</template>

<script lang="jsx" setup>
import { GlobalIcon } from '@/components/icon'
import CsTableColSettings from '@/components/settings/CsTableColSettings'
import BreadCrumb from '@/components/breadcrumb/BreadCrumb.vue'
import ForeignContractSearch from '@/view/equipment/foreignContract/ForeignContractSearch'
import ForeignContractTabs from '@/view/equipment/foreignContract/ForeignContractTabs'
import { ref, provide, onBeforeUnmount, computed, watchEffect, onMounted, createVNode } from 'vue'
import { useRoute } from 'vue-router'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { message, Modal } from 'ant-design-vue'
import ycCsApi from '@/api/ycCsApi'
import { useCommon } from '@/view/common/useCommon'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { useFieldMarking } from '@/utils/useFieldMarking'
import { localeContent } from '@/view/utils/commonUtil'
import { getAuditColumns } from '@/view/equipment/foreignContract/js/columns.jsx'
import { editStatus, EMPTY } from '@/view/common/constant'
import { getOptionsData } from '@/api/equipment/foreignContract'
import { observe, unobserve } from '@/utils/observe'
import { APPROVAL, EDIT, OPTIONS_CONFIG } from '@/view/equipment/foreignContract/js/handle'
import fieldNameMap from '@/view/equipment/foreignContract/audit/map'

const {
  ajaxUrl,
  showSearch,
  headSearch,
  handleShowSearch,
  handlerSearch,
  handlerRefresh,
  getList,
  show,
  tableLoading,
  dataSourceList,
  gridData,
  editConfig,
  onSelectChange,
  page,
  onPageChange
} = useCommon()

const { cmbShowRender } = useColumnsRender()
const { getBySidAndFormType } = useFieldMarking()

// 请求url
ajaxUrl.selectAllPage = ycCsApi.equipment.foreignContract.head.aeoList
ajaxUrl.selectAllPage = ycCsApi.equipment.foreignContract.head.list

// 配置表格列、导出列
const columns = getAuditColumns()

// 原始显示列
const originalColumns = ref((() => {
  for (let column of columns) {
    column.visible = true
  }
  return columns
})())

// 当前显示列
const showColumns = ref([...originalColumns.value])

// 自定义显示列更改回调
function customColumnChange(settingColumns) {
  showColumns.value = settingColumns.filter(item => item.visible === true)
}

// 表格唯一key
const tableKey = ref(window['$vueApp'] ? window.majesty.router.currentRoute.value.path : useRoute().path)

// 选项配置
const optionsConfig = ref({
  // 客户
  customerOptions: EMPTY.ARRAY,
  // 供应商
  supplierOptions: EMPTY.ARRAY,
  // 制单人
  makerOptions: EMPTY.ARRAY,
  // 商品类别
  productTypeOptions: EMPTY.ARRAY,
  // 价格条款
  priceTermsOptions: EMPTY.ARRAY,
  // 城市
  cityOptions: EMPTY.ARRAY,
  // 币制
  currOptions: EMPTY.ARRAY,
  // 港口
  portOptions: EMPTY.ARRAY,
  // 单位
  unitOptions: EMPTY.ARRAY
})

/**
 * 初始化选项配置
 */
async function initOptionsConfig() {
  const res = await getOptionsData()
  if (!res.success) {
    message.warn('选项配置初始化失败')
    return
  }
  const data = res.data
  optionsConfig.value.customerOptions = data.customerOptions
  optionsConfig.value.supplierOptions = data.supplierOptions
  optionsConfig.value.makerOptions = data.makerOptions
  optionsConfig.value.priceTermsOptions = data.priceTermsOptions
  optionsConfig.value.productTypeOptions = data.productTypeOptions
  const cityOptions = data.cityOptions
  cityOptions.forEach(item => {
    item.enName = item.ext
    delete item.ext
  })
  optionsConfig.value.cityOptions = cityOptions
  const currOptions = data.currOptions
  currOptions.forEach(item => {
    item.value = item.ext
    delete item.ext
  })
  optionsConfig.value.currOptions = currOptions
  optionsConfig.value.portOptions = data.portOptions
  optionsConfig.value.unitOptions = data.unitOptions
}

/**
 * 行内查看
 * @param record 数据记录
 */
function handleViewByRow(record) {
  show.value = false
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.editData = record
  editConfig.value.headId = record.id
}

/**
 * 返回
 * @param requireFlush 是否需要刷新
 */
function handleBack(requireFlush = true) {
  show.value = true
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.headId = ''
  editConfig.value.editData = {}
  if (requireFlush) {
    getList()
  }
}

// 列表表格样式
const listTableStyle = ref({
  minHeight: '300px'
})

// section高度
const dcSectionHeight = ref(0)
const dcSectionRef = ref(null)

watchEffect(() => {
  const currentHeight = dcSectionHeight.value - 130 - (showSearch.value ? 54 : 0)
  listTableStyle.value.minHeight = Math.max(currentHeight, 300) + 'px'
})

// 编辑数据
provide(EDIT, {
  config: editConfig,
  back: handleBack,
  toEdit: (data) => {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = data
    editConfig.value.headId = data.id
  },
  toShow: () => {
    if (!(editConfig.value.editStatus === editStatus.SHOW)) {
      editConfig.value.editStatus = editStatus.SHOW
    }
  },
  commonAddFlag: computed(() => editConfig.value.editStatus === editStatus.ADD),
  commonShowFlag: computed(() => editConfig.value.editStatus === editStatus.SHOW)
})

// 审核状态
const approval = ref({
  flag: true
})
provide(APPROVAL, approval)

// 选项配置
provide(OPTIONS_CONFIG, optionsConfig)

const passLoading = ref(false)

function handlePass() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择要审核通过的数据')
    return
  }
  passLoading.value = true
  try {
    const auditOpinion = ref('同意审批')
    Modal.confirm({
      title: '审核通过',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: () => {
        return (
          <div>
            <div style={{ marginTop: '10px' }}>
              <label style={{ display: 'block', marginBottom: '5px' }}>
                审核意见：
              </label>
              <textarea value={auditOpinion.value}
                        onInput={e => auditOpinion.value = e.target.value}
                        style={{
                          width: '100%',
                          height: '80px',
                          padding: '8px',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          resize: 'vertical'
                        }}
                        placeholder="请输入审核意见">
            </textarea>
            </div>
          </div>
        )
      },
      async onOk() {
        passLoading.value = true
        try {
          const params = {
            ids: gridData.selectedRowKeys,
            apprMessage: auditOpinion.value || '同意审批',
            businessType: '3',
            billType: 'contract'
          }
          const res = await window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.audit, params)
          if (res['code'] === 200) {
            message.success('审核通过成功')
            getList()
            gridData.selectedRowKeys = []
            gridData.selectedData = []
          } else {
            message.error(res['message'] || '审核失败')
          }
        } catch (error) {
          console.error('审核失败:', error)
          message.error('审核失败，请重试')
        } finally {
          passLoading.value = false
        }
      }
    })
  } finally {
    passLoading.value = false
  }
}

const returnLoading = ref(false)

async function handleReturn() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择要审核退回的数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据审核退回')
    return
  }
  returnLoading.value = true
  try {
    const id = gridData.selectedRowKeys[0]
    // 红标字段
    let markedFields = ''
    try {
      const fieldMarkings = await getBySidAndFormType(id, 'default')
      if (fieldMarkings && typeof fieldMarkings === 'object') {
        // 从fieldMarkings对象中筛选出值为'red'的字段名
        const redFieldNames = Object.keys(fieldMarkings).filter(key => fieldMarkings[key] === 'red')
        if (redFieldNames.length > 0) {
          // 将英文字段名转换为中文显示
          const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
          markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
        }
      }
    } catch (error) {
      console.warn('获取标红字段失败:', error)
    }
    // 审核意见输入框
    const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
    // 弹出审核退回确认框
    Modal.confirm({
      title: '审核退回',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: () => {
        return (
          <div>
            <div style={{ marginTop: '10px' }}>
              <label style={{ display: 'block', marginBottom: '5px' }}>
                审核意见：
              </label>
              <textarea value={auditOpinion.value}
                        onInput={e => auditOpinion.value = e.target.value}
                        style={{
                          width: '100%',
                          height: '80px',
                          padding: '8px',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          resize: 'vertical'
                        }}
                        placeholder="请输入审核意见">
            </textarea>
            </div>
          </div>
        )
      },
      async onOk() {
        returnLoading.value = true
        try {
          const params = {
            ids: gridData.selectedRowKeys,
            apprMessage: auditOpinion.value || '审批退回',
            businessType: '3',
            billType: 'contract'
          }
          const res = await window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.reject, params)
          if (res['code'] === 200) {
            message.success('审核退回成功')
            getList()
            // 清空选择
            gridData.selectedRowKeys = []
            gridData.selectedData = []
          } else {
            message.error(res['message'] || '审核退回失败')
          }
        } catch (error) {
          console.error('审核退回失败:', error)
          message.error('审核退回失败，请重试')
        } finally {
          returnLoading.value = false
        }
      }
    })
  } finally {
    returnLoading.value = false
  }
}

onMounted(async () => {
  await initOptionsConfig()
  getList()
  observe(dcSectionRef.value, config => {
    dcSectionHeight.value = config.height
  })
})

onBeforeUnmount(() => {
  unobserve(dcSectionRef.value)
})

defineOptions({
  name: 'EquipmentForeignContractAuditList'
})
</script>
