<template>
  <section class="dc-section">
    <!-- 操作按钮区域 -->
    <div v-if="showFlag" style="margin-bottom: 20px" />
    <div v-else class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:equipment-foreignContract-list:add']">
        <a-button size="small" @click="handleAdd">
          <template #icon>
            <global-icon type="plus" style="color:green" />
          </template>
          {{ localeContent('m.common.button.add') }}
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:equipment-foreignContract-list:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handleDelete">
          <template #icon>
            <global-icon type="delete" style="color:red" />
          </template>
          {{ localeContent('m.common.button.delete') }}
        </a-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <s-table
      :animate-rows="false"
      ref="tableRef"
      column-drag
      class="remove-table-border-add-bg"
      size="small"
      bordered
      :pagination="false"
      :columns="showColumns"
      :data-source="datasourceList"
      :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
      :loading="tableLoading"
      row-key="id"
      :style="tableStyle"
      :row-class-name="rowClassName"
      :range-selection="false"
    >
      <template #bodyCell="{ column, text, record, index}">
        <div v-if="['qty', 'unitPrice'].includes(column.dataIndex)">
          <a-input-number
            v-if="editableData[record.id]"
            v-model:value="editableData[record.id][column.dataIndex]"
            size="small"
            :controls="false"
            :formatter="formatter"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            :precision="column.precision"
            style="width: 100%"
            @change="handleNumberInputChange(column, index)"
          />
          <span v-else>{{ formatSpecifiedNumber(datasourceList[index][column.dataIndex], true, 2) }}</span>
        </div>

        <div v-else-if="['unit'].includes(column.dataIndex)">
          <cs-select v-if="editableData[record.id]" optionFilterProp="label" option-label-prop="key" allow-clear
                     show-search v-model:value="editableData[record.id][column.dataIndex]" style="width: 100%">
            <a-select-option class="cs-select-dropdown" v-for="item in selectOptions[column.dataIndex]"
                             :key="`${item.value} ${item.label}`" :value="item.value"
                             :label="`${item.value}${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
          <span v-else>
             {{
              getKeyValue(selectOptions[column.dataIndex], datasourceList[index][column.dataIndex], column.withValue)
            }}
          </span>
        </div>

        <div v-else-if="column.dataIndex === 'deliveryDate'">
          <a-date-picker
            v-if="editableData[record.id]"
            v-model:value="editableData[record.id][column.dataIndex]"
            :valueFormat="DATE_FORMAT.DATE"
            :format="DATE_FORMAT.DATE"
            :locale="zhCN"
            :placeholder="EMPTY.STRING"
            size="small"
            style="width: 100%"
          />
          <span v-else>{{ datasourceList[index][column.dataIndex] }}</span>
        </div>

        <div v-else-if="['gModel'].includes(column.dataIndex)">
          <a-input v-if="editableData[record.id]" v-model:value="editableData[record.id][column.dataIndex]"
                   size="small" maxlength="100" />
          <span v-else>{{ datasourceList[index][column.dataIndex] }}</span>
        </div>

        <div v-else-if="['moneyAmount', 'convertedTotalDollars'].includes(column.dataIndex)">
          <span>{{ getNumberCellValue(index, column.dataIndex) }}</span>
        </div>
      </template>
    </s-table>

    <!-- 分页区域 -->
    <div class="cs-pagination">
      <div class="cs-margin-right cs-list-total-data">
        <span class="summary-item">总数量：{{ showSummaryInfo.totalQty }}</span>
        <span class="summary-item">总金额：{{ showSummaryInfo.totalMoneyAmount }}</span>
        <span class="summary-item">总价折美元：{{ showSummaryInfo.totalConvertedDollars }}</span>
      </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                    :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </section>

  <foreign-contract-body-add-modal ref="addModalRef" @save-body="handleSaveBody" />
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import { GlobalIcon } from '@/components/icon'
import ForeignContractBodyAddModal from '@/view/equipment/foreignContract/body/ForeignContractBodyAddModal.vue'
import { computed, createVNode, inject, onBeforeUnmount, onMounted, ref, toRaw, watch, watchEffect } from 'vue'
import { message, Modal } from 'ant-design-vue'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { localeContent } from '@/view/utils/commonUtil'
import { getBodyColumns } from '@/view/equipment/foreignContract/js/columns'
import { deepClone } from '@/view/utils/common'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { debounced, removeDebounced } from '@/view/equipment/foreignContract/js/debounced'
import { getBodyList, getBodySummaryInfo, addBody, updateBody, deleteBody } from '@/api/equipment/foreignContract'
import { TABS_HEIGHT, EDIT, OPTIONS_CONFIG, DOLLAR_RATE } from '@/view/equipment/foreignContract/js/handle'
import { DATE_FORMAT, EMPTY } from '@/view/common/constant'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'

const tabsHeight = inject(TABS_HEIGHT)
const edit = inject(EDIT)
const optionsConfig = inject(OPTIONS_CONFIG)
const dollarRate = inject(DOLLAR_RATE)

const { getKeyValue, formatSpecifiedNumber } = useColumnsRender()

// 选项
const selectOptions = computed(() => {
  return {
    unit: optionsConfig.value.unitOptions
  }
})

// 通用显示标识
const showFlag = edit.commonShowFlag
// 通用新增标识
const addFlag = edit.commonAddFlag

// 显示列
const showColumns = ref(getBodyColumns().map(item => {
  item.visible = true
  return item
}))

// 表格样式
const tableStyle = ref({
  minHeight: '140px'
})

// 表格高度副作用函数
const stopWatchHeight = watchEffect(() => {
  const curHeight = tabsHeight.value - 625 + (showFlag.value ? 20 : 0)
  tableStyle.value.minHeight = (curHeight < 140 ? 140 : curHeight) + 'px'
})

// 显示列副作用函数
const stopWatchColumns = watchEffect(() => {
  if (showFlag.value) {
    showColumns.value = showColumns.value.filter(item => item.dataIndex !== 'operation')
  }
})

// 表格loading
const tableLoading = ref(false)

// 数据源列表（表格展示使用）
const datasourceList = ref([])
// 新增数据源列表
let addDatasourceList = []

// 网格数据
const gridData = ref({
  selectedRowKeys: [],
  selectedData: []
})

/**
 * 重置网格数据
 */
function resetGridData() {
  gridData.value = {
    selectedRowKeys: [],
    selectedData: []
  }
}

/**
 * 行类名
 * @param record 数据记录
 * @param index 数据下标
 */
function rowClassName(record, index) {
  const effectiveRecord = datasourceList.value[index]
  return effectiveRecord && effectiveRecord.loading ? 'loading-row' : 'none-loading-row'
}

/**
 * 获取数字单元格值
 * @param index 数据下标
 * @param columnIndex 列索引
 */
function getNumberCellValue(index, columnIndex) {
  const record = datasourceList.value[index]
  if (!record) {
    return EMPTY.STRING
  }
  return formatSpecifiedNumber(datasourceList.value[index][columnIndex], true, 2)
}

/**
 * 保留两位小数
 * @param num 数字
 */
function keepTwoDecimalFull(num) {
  let result = parseFloat(num)
  result = Math.round(num * 100) / 100
  let sx = result.toString()
  let posDecimal = sx.indexOf('.')
  if (posDecimal < 0) {
    posDecimal = sx.length
    sx += '.'
  }
  while (sx.length <= posDecimal + 2) {
    sx += '0'
  }
  return Number(sx)
}

// 格式化数字（整数千分位分隔）
function formatter(value) {
  if (value === 0) {
    return '0'
  }
  if (!value) {
    return ''
  }
  const parts = value.toString().split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  return parts.join('.')
}

// 分页信息
const page = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

/**
 * 分页变化函数
 * @param pageNumber 页号
 * @param pageSize 页大小
 */
function onPageChange(pageNumber, pageSize) {
  page.value.current = pageNumber
  page.value.pageSize = pageSize
  if (addFlag.value) {
    updateAddList()
  } else {
    getList()
  }
}

/**
 * 获取列表
 */
async function getList() {
  tableLoading.value = true
  try {
    const {
      data, total, pageIndex
    } = await getBodyList(edit.config.value['headId'], page.value.current, page.value.pageSize)
    page.value.total = total
    page.value.current = pageIndex
    datasourceList.value = data
    await realTimeUpdateSummaryInfo()
    syncEditData()
  } finally {
    tableLoading.value = false
    resetGridData()
  }
}

/**
 * 选中行变化回调函数
 * @param selectedRowKeys 行keys
 * @param rowSelectData 选中数据
 */
function onSelectChange(selectedRowKeys, rowSelectData) {
  gridData.value.selectedData = rowSelectData
  gridData.value.selectedRowKeys = selectedRowKeys
}

// 编辑数据
const editableData = ref({})

// 新增模态框ref
const addModalRef = ref(null)

/**
 * 新增
 */
function handleAdd() {
  addModalRef.value.openModal()
}

/**
 * 获取新增列表
 */
function getAddList() {
  if (!addFlag.value) {
    return EMPTY.ARRAY
  }
  addDatasourceList.forEach(item => {
    if (editableData.value.hasOwnProperty(item.id)) {
      const editData = editableData.value[item.id]
      item.gModel = editData['gModel']
      item.unit = editData['unit']
      item.qty = editData['qty']
      item.unitPrice = editData['unitPrice']
      item.moneyAmount = editData['moneyAmount']
      item.convertedTotalDollars = editData['convertedTotalDollars']
      item.deliveryDate = editData['deliveryDate']
      item.note = editData['note']
    }
  })
  return addDatasourceList
}

/**
 * 更新新增列表
 */
function updateAddList() {
  page.value.total = addDatasourceList.length
  const startIndex = (page.value.current - 1) * page.value.pageSize
  const endIndex = Math.min(startIndex + page.value.pageSize, addDatasourceList.length)
  datasourceList.value = addDatasourceList.slice(startIndex, endIndex)
  realTimeUpdateSummaryInfo()
}

/**
 * 新增表体
 */
async function handleSaveBody({ records, ids }) {
  addModalRef.value.saveLoading = true
  try {
    // add ---- insert into datasource, and copy data into editableData
    if (addFlag.value) {
      addModalRef.value.closeModal()
      resetGridData()
      const bodyRecords = records.map(record => getDefaultAddBody(record))
      addDatasourceList = bodyRecords.concat(addDatasourceList)
      bodyRecords.forEach(item => editableData.value[item.id] = deepClone(item))
      updateAddList()
      return
    }
    // update ---- send request to the server to add body
    const params = {
      headId: edit.config.value['headId'],
      materialIds: ids
    }
    const res = await addBody(params)
    if (!res.success) {
      message.error(res.message)
      return
    }
    message.success('新增表体成功')
    addModalRef.value.closeModal()
    await getList()
  } finally {
    addModalRef.value.saveLoading = false
  }
}

/**
 * 获取默认新增表体数据
 * @param record 记录
 */
function getDefaultAddBody(record) {
  return {
    id: record.id + '-' + new Date().getTime(),
    gName: record.gName,
    gModel: '',
    unit: '',
    qty: undefined,
    unitPrice: undefined,
    moneyAmount: undefined,
    deliveryDate: undefined,
    convertedTotalDollars: undefined,
    note: ''
  }
}

/**
 * 删除表体
 */
const deleteLoading = ref(false)

function handleDelete() {
  if (gridData.value.selectedRowKeys.length <= 0) {
    message.warn('请选择一条或多条要删除的数据')
    return
  }
  const doDelete = async () => {
    const ids = gridData.value.selectedRowKeys
    if (addFlag.value) {
      ids.forEach(id => {
        if (editableData.value[id]) {
          delete editableData.value[id]
        }
        addDatasourceList = addDatasourceList.filter(item => item.id !== id)
        updateAddList()
      })
      return
    }
    deleteLoading.value = true
    try {
      const res = await deleteBody(ids)
      if (res.success) {
        message.success('删除成功')
        await getList()
      } else {
        message.error(res.message)
      }
    } finally {
      deleteLoading.value = false
    }
  }
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk: async () => {
      await doDelete()
    }
  })
}

// 表格监听计数器
const tableWatcherCounter = new Map()

/**
 * 清理编辑数据
 */
function clearEditData() {
  if (!editableData.value) {
    return
  }
  const keys = Object.keys(editableData.value)
  const rawEditableData = toRaw(editableData.value)
  keys.forEach(key => {
    if (tableWatcherCounter.has(key)) {
      tableWatcherCounter.delete(key)
    }
    removeDebounced(key)
    if (rawEditableData.hasOwnProperty(key)) {
      const editData = editableData.value[key]
      if (editData && editData['_stopWatch'] && typeof editData['_stopWatch'] === 'function') {
        editData['_stopWatch']()
      }
    }
  })
}

/**
 * 同步编辑数据
 */
function syncEditData() {
  if (showFlag.value || addFlag.value) {
    return
  }
  // 清理编辑数据
  clearEditData()
  // 重新同步编辑数据
  const unprocessed = {}
  const ids = []
  datasourceList.value.forEach(item => {
    const cloneObj = deepClone(toRaw(item))
    cloneObj['_debounced'] = debounced(item.id)
    tableWatcherCounter.set(item.id, 0)
    unprocessed[item.id] = cloneObj
    ids.push(item.id)
  })
  editableData.value = unprocessed
  // 监听更改
  ids.forEach(id => {
    editableData.value[id]['_stopWatch'] = watch(() => editableData.value[id], editData => {
      // 增加计数
      tableWatcherCounter.set(id, (tableWatcherCounter.get(id) || 0) + 1)
      // 如果是第一次effect执行，则跳过
      if (tableWatcherCounter.get(id) === 1) {
        return
      }
      editData['_debounced'](async () => {
        const index = datasourceList.value.findIndex(item => item.id === id)
        datasourceList.value[index].loading = true
        try {
          // 请求更改
          const res = await updateBody(id, editData)
          if (!res || !res.success) {
            message.error('修改失败')
            return
          }
          const oldData = datasourceList.value[index]
          // 补正汇总信息
          correctionSummaryInfo({ ...res.data }, oldData)
          // 更新源数据源
          datasourceList.value[index] = res.data
        } finally {
          datasourceList.value[index].loading = false
        }
      }, 1500)()
    }, { deep: true, immediate: false })
  })
}


// 汇总信息（新增）
const newSummaryInfo = ref({
  totalQty: 0,
  totalMoneyAmount: 0,
  totalConvertedDollars: 0
})

// 汇总信息（修改）
const summaryInfo = ref({
  totalQty: 0,
  totalMoneyAmount: 0,
  totalConvertedDollars: 0
})

// 显示汇总信息（取新增或修改）
const showSummaryInfo = computed(() => {
  const summaryInfoValue = addFlag.value ? newSummaryInfo.value : summaryInfo.value
  const showSummary = {}
  Object.keys(toRaw(summaryInfoValue)).forEach(key => {
    showSummary[key] = formatSpecifiedNumber(String(Number(summaryInfoValue[key]) || 0), true, 2)
  })
  return showSummary
})

/**
 * 实时更新汇总信息
 * @param manual
 * @param options 选项
 */
async function realTimeUpdateSummaryInfo(manual = false, options) {
  if (addFlag.value) {
    newSummaryInfo.value.totalQty = keepTwoDecimalFull(addDatasourceList.map(item => editableData.value.hasOwnProperty(item.id)
      ? Number(editableData.value[item.id]['qty']) : Number(item['qty'])).map(item => item || 0)
      .reduce((acc, n) => acc + n, 0))
    newSummaryInfo.value.totalMoneyAmount = keepTwoDecimalFull(addDatasourceList.map(item => editableData.value.hasOwnProperty(item.id)
      ? Number(editableData.value[item.id]['moneyAmount']) : Number(item['moneyAmount'])).map(item => item || 0)
      .reduce((acc, n) => acc + n, 0))
    if (dollarRate.value || dollarRate.value === 0) {
      newSummaryInfo.value.totalConvertedDollars = keepTwoDecimalFull(addDatasourceList.map(item => editableData.value.hasOwnProperty(item.id)
        ? Number(editableData.value[item.id]['convertedTotalDollars']) : Number(item['convertedTotalDollars'])).map(item => item || 0)
        .reduce((acc, n) => acc + n, 0))
    } else {
      newSummaryInfo.value.totalConvertedDollars = 0
    }
    return
  }
  if (!manual) {
    await getSummaryInfoByRequestWhenUpdate()
    return
  }
  if (options) {
    const { summaryItem, newValue, oldValue } = options
    markUpDiffSummaryItemWhenUpdate(summaryItem, newValue, oldValue)
  }
}

/**
 * 处理数字输入框变化（作用为处理金额 = 单价 * 金额，及更新汇总信息）
 * @param column 列
 * @param index 数据下标
 */
function handleNumberInputChange(column, index) {
  if (!addFlag.value) {
    return
  }
  const columnIndex = column.dataIndex
  if (columnIndex === 'unitPrice' || columnIndex === 'qty') {
    const record = datasourceList.value[index]
    if (editableData.value.hasOwnProperty(record.id)) {
      const editRecord = editableData.value[record.id]
      const moneyAmount = keepTwoDecimalFull((Number(editRecord.qty) || 0) * (Number(editRecord.unitPrice) || 0))
      record.moneyAmount = moneyAmount
      editRecord.moneyAmount = moneyAmount
      if (dollarRate.value || dollarRate.value === 0) {
        const convertedTotalDollars = moneyAmount * dollarRate.value
        record.convertedTotalDollars = convertedTotalDollars
        editRecord.convertedTotalDollars = convertedTotalDollars
      } else {
        record.convertedTotalDollars = undefined
        editRecord.convertedTotalDollars = undefined
      }
    }
  }
  realTimeUpdateSummaryInfo(true)
}

/**
 * 请求获取汇总信息（修改时使用）
 */
async function getSummaryInfoByRequestWhenUpdate() {
  const params = { headId: edit.config.value['headId'] }
  const res = await getBodySummaryInfo(params)
  if (!res.success) {
    message.error(res.message)
    return
  }
  const summaryData = res.data
  summaryInfo.value.totalQty = summaryData['totalQty'] || 0
  summaryInfo.value.totalMoneyAmount = summaryData['totalMoneyAmount'] || 0
  summaryInfo.value.totalConvertedDollars = summaryData['totalConvertedDollars'] || 0
}

/**
 * 汇总项补足差异化（修改时使用）
 * @param summaryItem 汇总项
 * @param newValue 新值
 * @param oldValue 旧值
 */
function markUpDiffSummaryItemWhenUpdate(summaryItem, newValue, oldValue) {
  if (addFlag.value) {
    return
  }
  summaryInfo.value[summaryItem] = keepTwoDecimalFull(summaryInfo.value[summaryItem] + (newValue || 0) - (oldValue || 0))
}

/**
 * 补正汇总信息
 * @param newData 新数据
 * @param oldData 旧数据
 */
function correctionSummaryInfo(newData, oldData) {
  const items = ['qty', 'moneyAmount']
  items.forEach(item => {
    const option = {
      summaryItem: `total${item.charAt(0).toUpperCase()}${item.slice(1)}`,
      newValue: newData[item],
      oldValue: oldData[item]
    }
    realTimeUpdateSummaryInfo(true, option)
  })
  const dollarOption = {
    summaryItem: 'totalConvertedDollars',
    newValue: newData.convertedTotalDollars,
    oldValue: oldData.convertedTotalDollars
  }
  realTimeUpdateSummaryInfo(true, dollarOption)
}

/**
 * 变更为只读状态
 */
function toReadonly() {
  edit.toShow()
  clearEditData()
  editableData.value = {}
  getList()
}

/**
 * 刷新美元汇率
 */
function flushDollarRate() {
  if (!addFlag.value) {
    return
  }
  addDatasourceList.forEach(item => {
    const editData = toRaw(editableData.value[item.id])
    if ((editData.qty || editData.qty === 0) && (editData.unitPrice || editData.unitPrice === 0)) {
      const moneyAmount = editData.qty * editData.unitPrice
      item.moneyAmount = moneyAmount
      editData.moneyAmount = moneyAmount
      if (dollarRate.value || dollarRate.value === 0) {
        const convertedTotalDollars = moneyAmount * dollarRate.value
        item.convertedTotalDollars = convertedTotalDollars
        editData.convertedTotalDollars = convertedTotalDollars
      } else {
        item.convertedTotalDollars = undefined
        editData.convertedTotalDollars = undefined
      }
    }
  })
  updateAddList()
}

/**
 * 初始化数据源列表
 */
function initDatasourceList() {
  if (addFlag.value) {
    addDatasourceList = []
    datasourceList.value = []
    return
  }
  getList()
}

onMounted(() => {
  initDatasourceList()
})

onBeforeUnmount(() => {
  stopWatchHeight()
  stopWatchColumns()
  clearEditData()
})

defineExpose({ getAddList, getList, toReadonly, flushDollarRate })

defineOptions({
  name: 'EquipmentForeignContractBodyList'
})
</script>

<style scoped lang="less">
.summary-item {
  margin-left: 20px;
  position: relative;
  top: 0.5px;
}

/** loading动画 **/
@keyframes row-loading-animation {
  0% {
    transform: rotate(0deg)
  }

  50% {
    transform: rotate(180deg)
  }

  100% {
    transform: rotate(360deg)
  }
}

:deep(.loading-row) {
  transform-origin: 0 0;
}

/** 表格行蒙层 **/
:deep(.loading-row::before) {
  content: '';
  display: block;
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 100;
  opacity: 0.8;
  transform: translateZ(0) scale(0.97);
}

/** 表格行loading **/
:deep(.loading-row::after) {
  content: '';
  display: block;
  position: absolute;
  animation: row-loading-animation 1s linear infinite;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  box-shadow: 0 2px 0 0 #0099e5;
  left: 50%;
  right: 50%;
}
</style>
