import { defineAsyncComponent } from "vue"
export default [
  {
    path: '/tobacco/equipment/foreignContract',
    name: 'EquipmentForeignContractList',
    meta: {
      title: '外商合同'
    },
    component: defineAsyncComponent(() => import(/* webpackChunkName: "my-chunk-name" */ './foreignContract/ForeignContractList.vue'))
  },
  {
    path: '/audit/equipment/foreignContract',
    name: 'EquipmentForeignContractAuditList',
    meta: {
      title: '外商合同审核'
    },
    component: defineAsyncComponent(() => import(/* webpackChunkName: "my-chunk-name" */ './foreignContract/audit/ForeignContractAuditList.vue'))
  },
  {
    path: '/tobacco/equipment/bizIEquipmentAgentAgreementList',
    name: 'BizIEquipmentAgentAgreementList',
    meta: {
      icon: 'ios-document',
      title: '代理协议'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bizIEquipmentAgentAgreement/BizIEquipmentAgentAgreementList.vue"))
  },
  {
    path: '/audit/equipment/bizIEquipmentAgentAgreementList',
    name: 'BizIEquipmentAgentAgreementListAudit',
    meta: {
      icon: 'ios-document',
      title: '卷烟设备代理协议'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bizIEquipmentAgentAgreement/BizIEquipmentAgentAgreementListAudit.vue"))
  },
  {
    path: '/tobacco/equipment/bizIEquipmentPlanHeadList',
    name: 'BizIEquipmentPlanHeadList',
    meta: {
      icon: 'ios-document',
      title: '进货计划书'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./iPlan/bizIEquipmentPlanHead/BizIEquipmentPlanHeadList.vue"))
  },
]
