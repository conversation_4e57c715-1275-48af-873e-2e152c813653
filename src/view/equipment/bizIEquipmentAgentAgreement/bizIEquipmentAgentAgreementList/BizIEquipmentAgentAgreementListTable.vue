<template>
  <div class="contract-detail-table">
    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item">
        <a-button size="small" :loading="deleteLoading" @click="handlerDelete" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          {{localeContent('m.common.button.delete')}}
        </a-button>
      </div>
    </div>
    <s-table
        ref="tableRef"
        class="cs-action-item-modal-table remove-table-border-add-bg"
        bordered
        :pagination="false"
        :height="400"
        column-drag
        :data-source="tableData"
        :columns="getColumns"
        row-key="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'unit'">
          {{ formatUnit(record.unit) }}
        </template>
        <!--        <template v-if="column.dataIndex === 'curr'">-->
        <!--          {{ formatCurrency(record.curr) }}-->
        <!--        </template>-->
        <!--        <template v-if="column.dataIndex === 'goodsCategory'">-->
        <!--          {{ formatGoodsCategory(record.goodsCategory) }}-->
        <!--        </template>-->
      </template>
      <template v-if="props.editConfig.editStatus !== editStatus.SHOW" #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer, record }">
        <template v-if="column.dataIndex === 'remark'">
          <a-textarea
              :ref="editorRef"
              size="small"
              v-model:value="modelValue.value"
              style="width: 100%;height: 24px"
              @blur="() => {
                      handleNoteChange(record, modelValue.value);
                      // save();
                      // closeEditor();
                    }"
              @keydown.enter="() => {
                      handleNoteChange(record, modelValue.value);
                      // save();
                      // closeEditor();
                    }"
              @keydown.esc="closeEditor"
          />
        </template>
        <template v-if="column.dataIndex === 'quantity'">
          <a-input-number
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%;height: 24px"
            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            @blur="() => {
                      handleContractQuantityChange(record, modelValue.value);
                      // save();
                      // closeEditor();
                    }"
            @keydown.enter="() => {
                      handleContractQuantityChange(record, modelValue.value);
                      // save();
                      // closeEditor();
                    }"
            @keydown.esc="closeEditor"
          />
        </template>
        <template v-if="column.dataIndex === 'unitPrice'">
          <a-input-number
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%;height: 24px"
            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            @blur="() => {
                              handleUnitPriceChange(record, modelValue.value);
                              // save();
                              // closeEditor();
                            }"
            @keydown.enter="() => {
                              handleUnitPriceChange(record, modelValue.value);
                              // save();
                              // closeEditor();
                            }"
            @keydown.esc="closeEditor"
          />
        </template>
        <template v-if="column.dataIndex === 'shipDate'">
          <a-date-picker
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :locale="locale"
            @change="(date) => {
              handleShipDateChange(record, date);
            }"
          />
        </template>
      </template>
    </s-table>
    <!-- 分页 -->
    <div class=cs-pagination>
            <div class="cs-margin-right cs-list-total-data ">
              总数量：{{formatNumberNew(totalData.qtyTotal)}} ，总金额：{{formatNumberNew(totalData.decTotal)}}
            </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>
<script setup>
import {ref, reactive, watch, onMounted, createVNode, computed, h, nextTick} from 'vue';
import {message, Modal} from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import { usePCode } from "@/view/common/usePCode";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useCommon} from '@/view/common/useCommon'
import {editStatus} from "@/view/common/constant";
import { useMerchant } from "@/view/common/useMerchant"
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {updateContractList} from "@/api/auxiliaryMaterials/forContract/contractApi";
const { cmbShowRender, formatNumberNew } = useColumnsRender();
const { productTypeOptions, getProductTypeOptions } = useMerchant()
const {
  page
} = useCommon()
const { getPCode } = usePCode();
const pCode = ref('');

//单位
const unitOptions = ref([]);

const formatUnit = (code) => {
  const option = unitOptions.value.find(opt => opt.value === code);
  return option ? option.label : code;
};
// 获取列表后自动触发编辑器
const getList = async () => {
  try {
    const params = {
      headId: props.headId
    };
    window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.list}?page=${page.current}&limit=${page.pageSize}`,
        params
    ).then(res => {
      if (res.code === 200) {
        tableData.value = res.data || [];
        editableKeys.value = tableData.value.map(item => item.id);
        page.total = res.total;
        // 数据加载完成后，如果不是查看模式，触发编辑器
        if (props.editConfig.editStatus !== editStatus.SHOW) {
          setTimeout(() => {
            triggerEditor();
          }, 200);
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    })
  } catch (error) {
    message.error('获取数据失败');
  }
};
const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
const emit = defineEmits(['update:value', 'change']);
const deleteLoading = ref(false)
const tableRef = ref();
const selectedRowKeys = ref([]);
const tableData = ref([]);
const dataSource = ref([]);
const editableKeys = ref([]);
// 监听headId变化
watch(() => props.headId, (newVal) => {
  if (newVal) {
    getList();
    initIListSumData()
  }
}, { immediate: true });
const onPageChange = async (pageNumber, pageSize) => {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  await getList()
}
// 处理表格数据变化
// const handleTableChange = (value, key, column) => {
//   console.log('表格数据变化:', value, key, column);
//   emit('change', tableData.value);
// };


const handleContractQuantityChange = async (record, newValue) => {
  if (newValue === record.quantity) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, quantity: newValue };
      const response = await window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.update}/${record.id}`, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};


const handleShipDateChange = async (record, newValue) => {
  // 格式化日期为字符串进行比较
  const newDateStr = newValue
  const currentDateStr = record.shipDate;

  if (newDateStr === currentDateStr) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      const updatedRecord = { ...record, shipDate: newDateStr };
      const response = await window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.update}/${record.id}`, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

const handleNoteChange = async (record, newValue) => {
  if (newValue === record.remark) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, remark: newValue };
      const response = await window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.update}/${record.id}`, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
}

const handleUnitPriceChange  = async (record, newValue) => {
  if (newValue === record.unitPrice) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, unitPrice: newValue };
      const response = await window.majesty.httpUtil.putAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.update}/${record.id}`, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

/* 删除数据 */
const handlerDelete = () => {
  if (selectedRowKeys.value.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      window.majesty.httpUtil.deleteAction(`${ycCsApi.equipment.bizIEquipmentAgentAgreementList.delete}/${selectedRowKeys.value}`).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
          initIListSumData()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}
/* 定义汇总数据 */
const totalData = ref({
  qtyTotal:0,
  decTotal:0
})
// /* 获取表体汇总数据 */
const initIListSumData = () => {
  const params = {
    headId: props.headId
  };
  window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentAgentAgreementList.getSumTotal, params).then(res => {
    if (res.code === 200) {
      totalData.value  = {...res.data}
    }else {
      message.error(res.message)
    }
  })
}
// 计算属性：根据编辑状态动态生成列配置
const getColumns = computed(() => {
  return columns;
});
// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && tableData.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});
// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...tableData.value];
    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'quantity', rowKey: row.id });
      editConfigs.push({ columnKey: 'unitPrice', rowKey: row.id });
      editConfigs.push({ columnKey: 'shipDate', rowKey: row.id });
      editConfigs.push({ columnKey: 'remark', rowKey: row.id });
    });
    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        // 一次性打开所有单元格的编辑状态
        tableRef.value?.openEditor(editConfigs);
      }
    });
  }
};

// 页面加载完成后自动触发编辑状态
onMounted(() => {
  // 获取币种代码
  getPCode().then(res => {
    console.log('res', res)
    pCode.value = res;

    unitOptions.value = Object.entries(pCode.value.UNIT).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })
});
// 初始化列定义
const columns = [
  {
    title: '商品名称',
    width: 150,
    maxLength: 80,
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: '产品型号',
    width: 150,
    maxLength: 80,
    dataIndex: 'productModel',
    key: 'productModel',
  },
  {
    title: '数量',
    width: 150,
    maxLength: 80,
    dataIndex: 'quantity',
    key: 'quantity',
    editable: 'cellEditorSlot',
    customRender: ({ text }) => {
      return formatNumberNew(text);
    }
  },
  {
    title: '单位',
    width: 150,
    maxLength: 80,
    dataIndex: 'unit',
    key: 'unit',
  },
  {
    title: '单价',
    width: 150,
    maxLength: 80,
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    editable: 'cellEditorSlot',
    customRender: ({ text }) => {
      return formatNumberNew(text);
    }
  },
  {
    title: '金额',
    width: 150,
    maxLength: 80,
    dataIndex: 'totalAmount',
    key: 'totalAmount',
    editable: 'cellEditorSlot',
    customRender: ({ text }) => {
      return formatNumberNew(text);
    }
  },
  {
    title: '装运日期',
    width: 150,
    maxLength: 80,
    dataIndex: 'shipDate',
    key: 'shipDate',
    editable: 'cellEditorSlot',
  },
  {
    title: '备注',
    width: 150,
    maxLength: 80,
    dataIndex: 'remark',
    key: 'remark',
    editable: 'cellEditorSlot',
  },
];
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};
defineExpose({
  getTableData: () => tableData.value,
  reloadData: getList // 暴露刷新方法供父组件调用
});
</script>
<style scoped>
.contract-detail-table {
  margin-top: 16px;
}
</style>
