<template>
  <a-modal
    v-model:visible="props.visible"
    title="选择外商合同数据"
    width="1000px"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :keyboard="false"
    okText="保存"
    cancelText="关闭"
  >
    <!-- 查询条件 -->
    <div class="cs-search">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="合同号">
          <a-input
            v-model:value="searchForm.contractNo"
            placeholder="请输入合同号"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 计划列表 -->
    <div class="table-container" :style="{ minHeight: tableHeight + 'px' }">
      <s-table
        ref="planTableRef"
        class="cs-action-item"
        size="small"
        height="50vh"
        :scroll="{ y: tableHeight }"
        bordered
        :columns="columns"
        :data-source="planData"
        :row-key="getRowKey"
        :pagination="false"
        :loading="loading"
        :row-selection="{
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'seller'">
            {{ sellerValueFormat(record.seller) }}
          </template>
          <template v-if="column.dataIndex === 'buyer'">
            {{ sellerValueFormat(record.buyer) }}
          </template>
        </template>
      </s-table>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {insertContract} from "@/api/auxiliaryMaterials/forContract/contractApi";
import { useColumnsRender } from '@/view/common/useColumnsRender';
import { useMerchant } from "@/view/common/useMerchant"

const { cmbShowRender,formatNumberNew } = useColumnsRender();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 计划列表数据
const planData = ref([]);
const selectedKeys = ref([]);
const selectedPlan = ref(null);
const loading = ref(false);
const tableHeight = ref(300);

// 查询表单
const searchForm = reactive({
  contractNo: ''
});

// 获取供应商数据
const { merchantOptions, getMerchantOptions } = useMerchant()

// 列定义
const columns = [
  {
    title: '合同号',
    dataIndex: 'contractNo',
    width: 150
  },
  {
    title: '买方',
    dataIndex: 'buyer',
    width: 200
  },

  {
    title: '卖方',
    dataIndex: 'seller',
    width: 200
  },
  {
    title: '签约日期',
    dataIndex: 'signDate',
    width: 100
  },
  {
    title: '金额',
    dataIndex: 'totalAmount',
    width: 150,
    customRender: ({ text }) => {
      return formatNumberNew(text);
    }
  }
];


const sellerValueFormat = (value) => {
  if (!value) return '';
  return cmbShowRender(value, merchantOptions.value);
}

// 生成行唯一标识
const getRowKey = (record) => {
  return `${record.contractNo}`;
};

// 获取计划列表
const getPlanList = async () => {
  loading.value = true;
  try {
    const params = {
      contractNo: searchForm.contractNo
    };
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.equipment.bizIEquipmentAgentAgreement.forContractList}`,
      params
    );
    if (res.code === 200) {
      planData.value = res.data || [];
    } else {
      planData.value = [];
      message.error(res.message || '获取外商合同列表失败');
    }
  } catch (error) {
    planData.value = [];
    message.error('获取外商合同列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理重置
const handleReset = () => {
  searchForm.contractNo = '';
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理选择
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedKeys.value = selectedRowKeys;
  selectedPlan.value = selectedRows[0];
};

// 处理确认
const handleOk = () => {
  if (!selectedPlan.value) {
    message.warning('请选择一条数据');
    return;
  }
  if (selectedKeys.value.length > 1){
    message.warning('请选择一条数据');
    return;
  }
  const params = {
    id: selectedPlan.value.id
  };
  try {
    window.majesty.httpUtil.postAction(ycCsApi.equipment.bizIEquipmentAgentAgreement.insert, params).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
        emit('select', res.data);
        handleCancel();
      } else {
        message.error(res.message);
      }
    })
  } catch (error) {
    console.error('新增失败', error)
  }
};

// 处理取消
const handleCancel = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  planData.value = [];
  emit('update:visible', false);
};

// 监听visible变化，当显示时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置表格高度
    tableHeight.value = 300;
    getPlanList();
  } else {
    planData.value = [];
    selectedKeys.value = [];
    selectedPlan.value = null;
  }
});

// 组件卸载时清理数据
onMounted(() => {
  planData.value = [];
  selectedKeys.value = [];
  selectedPlan.value = null;
  getMerchantOptions()
});
</script>

<style lang="less" scoped>
.cs-search {
  margin-bottom: 16px;
}

.table-container {
  position: relative;
  min-height: 300px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  .ant-table {
    .ant-table-row {
      &.ant-table-row-selected > td {
        background-color: #e6f7ff;
      }
      &:hover > td {
        background-color: #fafafa;
      }
    }
  }
}

:deep(.ant-radio-wrapper) {
  .ant-radio-checked {
    .ant-radio-inner {
      border-color: #1890ff;
      &::after {
        background-color: #1890ff;
      }
    }
  }
}
</style>
