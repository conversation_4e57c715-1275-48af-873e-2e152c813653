<template>
  <a-modal
      v-model:visible="props.visible"
      title="选择外商合同"
      width="1000px"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
      :keyboard="false"
      okText="保存"
      cancelText="关闭"
  >
    <!-- 查询条件 -->
    <div class="cs-search">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="合同号">
          <a-input
              v-model:value="searchForm.contractNo"
              placeholder="请输入合同号"
              allow-clear
              style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 计划列表 -->
    <div class="table-container" :style="{ minHeight: tableHeight + 'px' }">
      <s-table
          ref="planTableRef"
          class="cs-action-item"
          size="small"
          height="50vh"
          :scroll="{ y: tableHeight }"
          bordered
          :columns="columns"
          :data-source="planData"
          :row-key="getRowKey"
          :pagination="false"
          :loading="loading"
          :row-selection="{
          // type: 'radio',
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange
        }"
      >
      </s-table>
    </div>
  </a-modal>
</template>

<script setup>
import {ref, reactive, watch, onMounted} from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {insertEquipmentPlan} from "@/api/equipment/equipmentPlanApi";
import {useMerchant} from "@/view/common/useMerchant";
import {useColumnsRender} from "@/view/common/useColumnsRender";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 计划列表数据
const planData = ref([]);
const selectedKeys = ref([]);
const selectedPlan = ref([]);
const loading = ref(false);
const tableHeight = ref(300);

// 查询表单
const searchForm = reactive({
  contractNo: ''
});

// 列定义
const columns = [
  {
    title: '合同号',
    dataIndex: 'contractNo',
    width: 150
  },
  {
    title: '卖家',
    dataIndex: 'seller',
    width: 160,
  },
  {
    title: '创建人',
    dataIndex: 'createByName',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180
  }
];

// 生成行唯一标识
const getRowKey = (record) => {
  return record.id || record.contractNo;
};

// 获取计划列表
const getPlanList = async () => {
  loading.value = true;
  try {
    const params = {
      contractNo: searchForm.contractNo
    };
    const res = await window.majesty.httpUtil.postAction(
        ycCsApi.equipment.bizIEquipmentPlanHead.listForPlan,
        params
    );
    if (res.code === 200) {
      planData.value = res.data || [];
    } else {
      planData.value = [];
      message.error(res.message || '获取计划列表失败');
    }
  } catch (error) {
    planData.value = [];
    message.error('获取计划列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  selectedKeys.value = [];
  selectedPlan.value = [];
  getPlanList();
};

// 处理重置
const handleReset = () => {
  searchForm.contractNo = '';
  selectedKeys.value = [];
  selectedPlan.value = [];
  getPlanList();
};

// 处理选择
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedKeys.value = selectedRowKeys;
  selectedPlan.value = selectedRows;
};

// 确认选择
const handleOk = async () => {
  if (!selectedKeys.value || selectedKeys.value.length === 0) {
    message.warning('请至少选择一个计划');
    return;
  }
  if (!selectedKeys.value || selectedKeys.value.length > 1) {
    message.warning('只能选择一个计划');
    return;
  }

  try {
    // 准备请求参数，将选中行的ID传递给后端
    const params = {
      forContractIdList: selectedKeys.value
    };

    // 调用后端接口
    const res = await insertEquipmentPlan(params);

    if (res.code === 200) {
      message.success('新增成功!');
      emit('select', res.data);
      emit('update:visible', false);
    } else {
      message.error(res.message || '新增失败');
    }
  } catch (error) {
    console.error('新增失败:', error);
    message.error('新增失败，请重试');
  }
};

const handleCancel = () => {
  emit('update:visible', false);
};

// 监听visible变化，当显示时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置表格高度
    tableHeight.value = 300;
    getPlanList();
  } else {
    planData.value = [];
    selectedKeys.value = [];
    selectedPlan.value = [];
  }
});
// 组件卸载时清理数据
onMounted(() => {
  planData.value = [];
  selectedKeys.value = [];
  selectedPlan.value = [];
});

</script>

<style scoped>
.table-container {
  margin-top: 16px;
}
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}
.table-container {
  position: relative;
  min-height: 300px;
}
</style>
