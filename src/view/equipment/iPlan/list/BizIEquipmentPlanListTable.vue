<template>
  <div class="buy-contract-detail-table">

    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
<!--      <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-aggrContract-list:add']">
        <a-button size="small" :loading="addLoading" @click="showProductSelector" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="plus" style="color:green"/>
          </template>
          {{ localeContent('m.common.button.add') }}
        </a-button>
      </div>

      <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-aggrContract-list:edit']">
        <a-button size="small" :loading="editLoading" @click="handlerEdit" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="form" style="color:orange"/>
          </template>
          {{ localeContent('m.common.button.update') }}
        </a-button>
      </div>-->

      <div class="cs-action-btn-item" v-has="['yc-cs:equipment-plan-list:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handlerDelete" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          {{ localeContent('m.common.button.delete') }}
        </a-button>
      </div>

      <!--      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-plan-list:import']">
              <a-button size="small" :loading="importLoading" @click="handlerImport" v-show="props.editConfig.editStatus !== editStatus.SHOW">
                <template #icon>
                  <GlobalIcon type="file-excel" style="color:deepskyblue"/>
                </template>
                {{ localeContent('m.common.button.import') }}
              </a-button>
            </div>-->
    </div>
    <s-table
      ref="tableRef"
      class="cs-action-item-modal-table remove-table-border-add-bg"
      bordered
      :custom-row="customRow"
      :data-source="tableData"
      :columns="getColumns"
      row-key="id"
      :pagination="false"
      column-drag
      :loading="tableLoading"
      @blur="handleBlur"
      @keydown.esc="handleBlur"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <!-- 编辑状态的输入字段 -->
        <template v-if="typeof column.editable === 'function' ? column.editable(record) : column.editable">

          <!-- 单位字段使用下拉框 -->
          <template v-if="column.dataIndex === 'unit'">
            <a-select
              :value="editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]"
              style="width: 100%"
              placeholder="请选择单位"
              :options="unitList"
              @change="value => {
                if (editableData[record.id]) {
                  editableData[record.id][column.dataIndex] = value;
                } else {
                  record[column.dataIndex] = value;
                }
              }"

            ></a-select>
          </template>
          <!-- 预计交货日期字段使用日期控件 -->
          <template v-else-if="column.dataIndex === 'estDeliveryDate'">
            <a-date-picker
              size="small"
              :value="editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]"
              style="width: 100%"
              placeholder="请选择预计交货日期"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              @change="value => {
                if (editableData[record.id]) {
                  editableData[record.id][column.dataIndex] = value;
                } else {
                  record[column.dataIndex] = value;
                }
              }"
            />
          </template>
          <!-- 备注字段 -->
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input
              :value="editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]"
              @change="e => {
                if (editableData[record.id]) {
                  editableData[record.id][column.dataIndex] = e.target.value;
                } else {
                  record[column.dataIndex] = e.target.value;
                }
              }"
              placeholder="请输入备注"
              @blur="() => validateRowData(record.id)"
            />
          </template>
          <!-- 折扣率字段使用带百分号的数字输入框 -->
          <!--          <template v-else-if="column.dataIndex === 'discountRate'">-->
          <!--            <a-input-number-->
          <!--              :value="editableData[record.id][column.dataIndex]"-->
          <!--              style="width: 100%"-->
          <!--              :formatter="value => `${value}%`"-->
          <!--              :parser="value => value.replace('%', '')"-->
          <!--              @change="value => {-->
          <!--                editableData[record.id][column.dataIndex] = value;-->
          <!--                inputTotal(record.id);-->
          <!--              }"-->
          <!--              @blur="() => validateRowData(record.id)"-->
          <!--              :data-field="column.dataIndex"-->
          <!--            />-->
          <!--          </template>-->

          <!-- 数量字段 -->
          <template v-else-if="column.dataIndex === 'qty'">
            <a-input-number
              :value="editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]"
              style="width: 100%"
              placeholder="请输入数量"
              :formatter="value => {
                if (!value) return '';
                const parts = value.toString().split('.');
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                return parts.join('.');
              }"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @change="value => {
                if (editableData[record.id]) {
                  editableData[record.id][column.dataIndex] = value;
                  inputTotal(record.id);
                } else {
                  record[column.dataIndex] = value;
                  calculateAmount(record);
                }
              }"
              @blur="() => validateRowData(record.id)"
              :data-field="column.dataIndex"
            />
          </template>
          <!-- 单价字段 -->
          <template v-else-if="column.dataIndex === 'unitPrice'">
            <a-input-number
              :value="editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]"
              style="width: 100%"
              placeholder="请输入单价"
              :formatter="value => {
                if (!value) return '';
                const parts = value.toString().split('.');
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                return parts.join('.');
              }"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @change="value => {
                if (editableData[record.id]) {
                  editableData[record.id][column.dataIndex] = value;
                  inputTotal(record.id);
                } else {
                  record[column.dataIndex] = value;
                  calculateAmount(record);
                }
              }"
              @blur="() => validateRowData(record.id)"
              :data-field="column.dataIndex"
            />
          </template>

        </template>

        <!-- 非编辑状态的各种字段渲染 -->
        <template v-else>
          <!-- 单位字段显示 -->
          <template v-if="column.dataIndex === 'unit'">
            {{ formatUnit(editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]) }}
          </template>
          <!-- 预计交货日期字段显示 -->
          <template v-else-if="column.dataIndex === 'estDeliveryDate'">
            {{ formatDate(editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]) }}
          </template>
          <!-- 数值字段格式化显示 -->
          <template v-else-if="['quantity', 'unitPrice', 'amount'].includes(column.dataIndex)">
            {{ formatNumber(editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex]) }}
          </template>
          <!-- 其他普通字段 -->
          <template v-else>
            {{ editableData[record.id] ? editableData[record.id][column.dataIndex] : record[column.dataIndex] }}
          </template>
        </template>
      </template>

    </s-table>

    <!-- 添加商品选择弹窗 -->
    <a-modal
      v-model:visible="productSelectorVisible"
      title="选择物料信息"
      width="800px"
      okText="保存"
      cancelText="返回"
      @ok="handleSelectProducts"
      @cancel="productSelectorVisible = false"
    >
      <a-spin :spinning="productLoading">
        <!-- 添加查询区域 -->
        <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
          <span style="margin-right: 8px;">商品名称：</span>
          <a-input
            v-model:value="gNameKeyword"
            placeholder="请输入商品名称"
            style="width: 200px;"
            @keyup.enter="handleSearchProducts"
          />
          <a-button type="primary" @click="handleSearchProducts">查询</a-button>
        </div>
        <a-table
          :columns="productColumns"
          :data-source="productList"
          :pagination="false"
          :scroll="{ y: 400 }"
          :row-selection="{
            selectedRowKeys: selectedProductKeys,
            onChange: onProductSelectChange,
            type: 'checkbox',
            preserveSelectedRowKeys: false
          }"
          :row-key="record => record.id || record.productCode || Date.now() + Math.random()"
        >
        </a-table>
      </a-spin>
    </a-modal>


    <!-- 导入数据 -->

    <!-- 分页 -->
    <div class="cs-pagination">
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, watch, onMounted, createVNode, computed, h, nextTick} from 'vue';
import dayjs from 'dayjs';
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';

// 添加格式化数字的辅助函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '';
  }

  // 分别处理整数部分和小数部分
  const parts = num.toFixed(2).split('.');
  // 只对整数部分添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  // 组合整数和小数部分
  return parts.join('.');
};
import {message, Modal, Tag} from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {validateDetailData} from "@/api/nonAuxiliaryMaterials/aggrContract/AggrContractApi";
import { usePCode } from "@/view/common/usePCode";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useCommon} from '@/view/common/useCommon'
import {editStatus,productClassify} from "@/view/common/constant";
import {useColumnsRender} from "@/view/common/useColumnsRender";


import {updateBizErpIOrderList} from "@/api/cs_api_constant";

/* 导入 */
import {ImportIndex} from 'yao-import'
import { useImport } from "@/view/common/useImport"
import {deleteEquipmentPlanPayNotify} from "@/api/equipment/equipmentPlanApi";
let { importConfig } = useImport()
const importShow = ref(false)

const {
  page,
  tableLoading
} = useCommon()

const { getPCode } = usePCode();
const pCode = ref('');
const detailEditStatus=ref(editStatus.SHOW);

// 日期选择器本地化
const locale = zhCN;

// 格式化单位显示
const formatUnit = (code) => {
  const option = unitList.value.find(opt => opt.value === code);
  return option ? ` ${option.label}` : code;
};

// 格式化商品类别显示
const formatCategory = (value) => {
  if (!value) return '';

  // 从商品列表中查找对应的类别名称
  const product = productList.value.find(item => item.merchandiseCategories === value);
  if (product && product.categoryName) {
    return `${value} ${product.categoryName}`;
  }

  return value;
};

// 格式化日期显示
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string') {
    // 如果已经是 YYYY-MM-DD 格式，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return date;
    }
    // 尝试解析其他格式的日期字符串
    const parsed = dayjs(date);
    return parsed.isValid() ? parsed.format('YYYY-MM-DD') : '';
  }
  if (date instanceof Date) {
    return dayjs(date).format('YYYY-MM-DD');
  }
  // 如果是 dayjs 对象
  if (dayjs.isDayjs(date)) {
    return date.format('YYYY-MM-DD');
  }
  return '';
};
// ... existing code ...
const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 获取基础数据
onMounted(() => {
  getUnitList()
  loadProducts('')

  // 如果是通过列表编辑按钮进入，设置所有行为可编辑状态
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    detailEditStatus.value = editStatus.EDIT;
  }
})



const emit = defineEmits(['update:value', 'change']);

const deleteLoading = ref(false);
const addLoading = ref(false); // 新增按钮 loading
const editLoading = ref(false); // 编辑按钮 loading
const importLoading = ref(false); // 导入按钮 loading
const { cmbShowRender } = useColumnsRender()

const tableRef = ref();
const selectedRowKeys = ref([]);
const tableData = ref([]);
const editableKeys = ref([]);

// 添加汇总数据对象
const totalData = ref({
  qtyTotal: 0,
  decTotal: 0
});

// 添加一个状态变量，用于跟踪新增行的 id
const newRowSid = ref(null);

// 添加一个新的数组来跟踪新增的行
const newAddedRows = ref([]);

// 添加 editableData 对象来跟踪编辑状态
const editableData = reactive({});



// 添加商品选择相关变量
const productSelectorVisible = ref(false);
const productList = ref([]);
const productLoading = ref(false);
const gNames = ref([]);
const selectedProductKeys = ref([]);
const selectedProducts = ref([]);
const unitList = ref([])
const getUnitList = async () => {
  const res = await window.majesty.httpUtil.postAction(ycCsApi.bizInComingHead.getUnitList,{})
  if (res.code === 200) {
    // 确保单位列表格式为 code + label
    unitList.value = res.data.map(item => ({
      label: `${item.value} ${item.label}`,
      value: item.value
    }));
  }
}


// 添加商品名称查询相关变量
const gNameKeyword = ref('');



// 商品列表的列定义
const productColumns = [
  {
    title: '商品名称',
    dataIndex: 'gName',
    key: 'gName',
    resizable:"true",
    width: 180
  },
  {
    title: '英文全称',
    dataIndex: 'fullEnName',
    width: 200,
    resizable:"true",
    key: 'fullEnName'
  },
  {
    title: '商品类别',
    dataIndex: 'merchandiseCategories',
    key: 'merchandiseCategories',
    resizable:"true",
    width: 200,
    // 自定义渲染，合并显示商品类别代码和名称
    customRender: ({ text, record }) => {
      return record.merchandiseCategories && record.categoryName
        ? `${record.merchandiseCategories} ${record.categoryName}`
        : text;
    }
  },{
    title: '条形码',
    dataIndex: 'barCode',
    resizable:"true",
    key: 'barCode',
    width: 120,
    // customRender: ({ text }) => {
    //   return formatNumber(text);
    // }
  }
  /*  {
      title: '克重',
      dataIndex: 'grammage',
      width: 120,
      resizable:"true",
      key: 'grammage',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },*/
  /*  {
      title: '计量单位',
      dataIndex: 'unit',
      width: 100,
      resizable: "true",
      key: 'unit',
      customRender: ({text}) => {
        return cmbShowRender(text, unitList.value)
      }
    },{
      title: '进口计量单位',
      dataIndex: 'unitI',
      width: 100,
      resizable:"true",
      key: 'unitI',
      customRender: ({text}) => {
        return cmbShowRender(text, unitList.value)
      }
    },*/
  // {
  //   title: '启用状态',
  //   dataIndex: 'dataState',
  //   key: 'dataState',
  //   resizable:"true",
  //   width: 180,
  //   customRender: ({ text }) => {
  //     const tagColor = text === '1' ? 'error' : 'success';
  //     return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.dataStatus))
  //   }
  // },
];

// 监听headId变化
watch(() => props.headId, (newVal) => {
  if (newVal) {
    getList();
  }
}, { immediate: true });

// 现在字段总是可编辑的，不需要监听编辑状态变化

// 获取列表数据
const getList = async () => {
  tableLoading.value = true
  try {
    const params = {
      headId: props.headId
    };
    window.majesty.httpUtil.postAction(`${ycCsApi.equipment.bizIEquipmentPlanList.list}?page=${page.current}&limit=${page.pageSize}`,
      params
    ).then(res => {
      if (res.code === 200) {
        tableData.value = res.data || [];
        editableKeys.value = tableData.value.map(item => item.id);
        page.total = res.total || 0;
        // 计算汇总数据
        // calculateTotals();

        // 将所有行设置为编辑状态
        nextTick(() => {
          if (tableData.value && tableData.value.length > 0) {
            tableData.value.forEach(record => {
              // 只对没有下游数据关联的行设置编辑状态
              if (record.hasCtr !== '1') {
                editableData[record.id] = JSON.parse(JSON.stringify(record));
                // 确保金额计算正确
                if (editableData[record.id].unitPrice && editableData[record.id].qty) {
                  editableData[record.id].amount = totalCount(editableData[record.id]);
                }
              }
            });
          }
        });

        // 如果是通过列表编辑按钮进入，设置所有行为可编辑状态
        if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
          // 将所有没有下游数据关联的行设置为编辑状态
          tableData.value.forEach(record => {
            if (record.hasCtr !== '1') {
              editableData[record.id] = JSON.parse(JSON.stringify(record));
              // 确保数量不为0，如果是0则设为空字符串
              if (editableData[record.id].qty === 0) {
                editableData[record.id].qty = '';
              }
              // 确保金额计算正确
              if (editableData[record.id].unitPrice && editableData[record.id].qty) {
                editableData[record.id].amount = totalCount(editableData[record.id]);
              }
            }
          });
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    }).finally(() => {
      tableLoading.value = false
    })
  } catch (error) {
    message.error('获取数据失败');
  }
};

// 处理分页变化
const onPageChange = async (pageNumber, pageSize) => {
  page.current = pageNumber;
  page.pageSize = pageSize;
  await getList();
};

// 计算汇总数据
const calculateTotals = () => {
  let qtyTotal = 0;
  let decTotal = 0;

  tableData.value.forEach(item => {
    // 优先使用编辑数据，如果没有则使用原始数据
    const currentData = editableData[item.id] || item;

    // 数量汇总
    if (currentData.qty) {
      const qty = parseFloat(currentData.qty);
      if (!isNaN(qty)) {
        qtyTotal += qty;
      }
    }

    // 总金额汇总
    if (currentData.amount) {
      const amount = parseFloat(currentData.amount);
      if (!isNaN(amount)) {
        decTotal += amount;
      }
    }
  });

  totalData.value = {
    qtyTotal: qtyTotal,
    decTotal: decTotal
  };
};

// 处理表格数据变化
// const handleTableChange = (value, key, column) => {
//   emit('change', tableData.value);
// };

/**
 * 单元格编辑保存
 * @param res
 * @returns {Promise<*>}
 */
const handleCellEdit = async (record, dataIndex, newValue) => {
  if (newValue === record[dataIndex]) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      tableData.value[rowIndex][dataIndex] = newValue;

      // 这里调用您的后端API
      const response = await updateBuyContractList(record.id, { ...record, [dataIndex]: newValue });
      if (response.code === 200) {
        // message.success('修改成功!');
        return newValue;
      } else {
        message.error(response.message || '修改失败');
        return record[dataIndex];
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
    return record[dataIndex];
  }
};
const customRow = (record) => {
  return {
    onDblclick: (event) => {
      // 如果双击的是输入框或其他表单元素，不处理行双击事件
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' ||
        event.target.closest('.ant-input') || event.target.closest('.ant-select') ||
        event.target.closest('.ant-date-picker')) {
        return;
      }
      handleRowDblclick(record);
    },
    style: {cursor: 'pointer'}
    // 移除 onclick 事件，取消点击行时选中行的功能
    // 保留复选框选择功能
  };
};
const handleRowDblclick = (record) => {
  // 现在字段总是可编辑的，双击不需要特殊处理
};

// 添加缺失的 handleBlur 函数
const handleBlur = () => {
  // 对于 AggrContract，字段总是可编辑的，blur 事件不需要特殊处理
  // 这里可以添加一些通用的失焦处理逻辑，比如保存数据等
};

// 现在字段总是可编辑的，不需要 edit 函数
// 计算金额的函数
const calculateAmount = (record) => {
  if (!record) return;

  const qty = parseFloat(record.qty) || 0;
  const unitPrice = parseFloat(record.unitPrice) || 0;

  // 计算金额 = 数量 × 单价
  record.amount = qty * unitPrice;

  // 重新计算汇总
  calculateTotals();
};

// 添加 inputTotal 函数，用于 editableData 的计算
const inputTotal = (id) => {
  if (editableData[id]) {
    if (editableData[id].unitPrice !== null && editableData[id].qty !== null && editableData[id].qty !== '' && editableData[id].qty !== undefined) {
      editableData[id].amount = totalCount(editableData[id]);
    }
    if (editableData[id].qty === undefined || editableData[id].qty === null || editableData[id].qty === '') {
      editableData[id].amount = 0;
    }
    // 重新计算汇总
    calculateTotals();
  }
};
const totalCount = (row) => {
  const qty = parseFloat(row.qty);
  const unitPrice = parseFloat(row.unitPrice);

  // 检查是否为有效数字，避免NaN结果
  if (isNaN(qty) || isNaN(unitPrice)) {
    return 0;
  }

  const total = roundToDecimal(qty*unitPrice,5)
  return total !== null ? total : 0
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

// 校验单行表体数据
const validateRowData = async (id) => {
  const record = tableData.value.find(item => item.id === id);
  if (!record) return true;

  // 构建校验数据对象
  const validateData = { ...record };

  // 如果有编辑数据，使用编辑数据覆盖
  if (editableData[id]) {
    const editableFields = ['productName', 'productModel','quantity', 'unit', 'unitPrice',  'estDeliveryDate', 'remark', 'amount'];
    editableFields.forEach(field => {
      if (editableData[id][field] !== undefined) {
        validateData[field] = editableData[id][field];
      }
    });
  }

  // 确保 headId 字段存在
  validateData.headId = props.headId;

  try {
   /* const response = await validateDetailData(validateData);
    if (response.success === true) {
      return true;
    } else {
      message.error(response.message);
      return false;
    }*/
  } catch (error) {
    message.error('数据校验失败，请重试');
    return false;
  }
};

// 现在数据直接保存在 tableData 中，不需要单独的保存函数

// 现在字段总是可编辑的，不需要取消编辑函数



// 现在字段总是可编辑的，不需要编辑按钮处理函数

/* 删除数据 */
const handlerDelete = () => {
  if (selectedRowKeys.value.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      try {
        // 将选中的行分为新增的和已存在的
        const newAddedIds = [];
        const existingIds = [];

        selectedRowKeys.value.forEach(id => {
          // 检查是否为新增的行
          const record = tableData.value.find(item => item.id === id);
          if (record && (record.isNewAdded === true || newAddedRows.value.includes(id) || id === newRowSid.value)) {
            newAddedIds.push(id);
          } else {
            existingIds.push(id);
          }
        });

        // 处理已存在的行（调用后端API删除）
        if (existingIds.length > 0) {
          deleteEquipmentPlanPayNotify(existingIds).then(res => {
            if (res.code === 200) {
              // 从tableData中删除已存在的行
              tableData.value = tableData.value.filter(row => !existingIds.includes(row.id));
              // 从editableData中删除
              existingIds.forEach(id => {
                delete editableData[id];
              });
            } else {
              message.error(res.message || '删除失败');
            }
          });
        }

        // 处理新增的行（从前端数组中删除）
        if (newAddedIds.length > 0) {
          // 从newAddedRows中删除
          newAddedRows.value = newAddedRows.value.filter(id => !newAddedIds.includes(id));

          // 从tableData中删除
          tableData.value = tableData.value.filter(row => !newAddedIds.includes(row.id));

          // 从editableData中删除
          newAddedIds.forEach(id => {
            delete editableData[id];
          });

          // 如果有与newRowSid匹配的ID，重置newRowSid
          if (newAddedIds.includes(newRowSid.value)) {
            newRowSid.value = null;
          }
        }

        message.success("删除成功！");
        selectedRowKeys.value = [];
        // 重新计算汇总数据
        calculateTotals();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败，请重试');
      } finally {
        deleteLoading.value = false;
      }
    },
    onCancel() {
    },
  });
}

// 计算属性：根据编辑状态动态生成列配置
const getColumns = computed(() => {
  return columns.map(column => {
    return {
      ...column,
      editable: (record) => {
        // 如果有下游数据关联，禁止编辑
        if (record.hasCtr === '1') {
          return false;
        }

        // 如果是查看模式，不可编辑
        if (props.editConfig.editStatus === editStatus.SHOW) {
          return false;
        }

        // 金额字段始终不可编辑（系统计算）
        if (column.dataIndex === 'amount') {
          return false;
        }

        // 商品名称和商品类别从弹框选择后置灰，不允许直接编辑
        if (['goodsName', 'goodsCategory'].includes(column.dataIndex)) {
          return false;
        }

        // 如果该记录在编辑状态中，特定字段可编辑
        if (editableData[record.id]) {
          return [ 'estDeliveryDate'].includes(column.dataIndex);
        }

        // 其他情况下，字段总是可编辑（商品描述、数量、单位、单价、交货日期、备注）
        return ['estDeliveryDate'].includes(column.dataIndex);
      }
    };
  });
});

// 初始化列定义
const columns = [
  {
    title: '商品名称',
    width: 150,
    maxLength: 80,
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: '产品型号',
    width: 150,
    maxLength: 80,
    dataIndex: 'productModel',
    key: 'productModel',
  },
  {
    title: '单位',
    width: 150,
    maxLength: 80,
    dataIndex: 'unit',
    key: 'unit',
  },
  {
    title: '数量',
    width: 150,
    maxLength: 80,
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: '单价',
    width: 150,
    maxLength: 80,
    dataIndex: 'unitPrice',
    key: 'unitPrice',
  },
  {
    title: '金额',
    width: 150,
    maxLength: 80,
    dataIndex: 'amount',
    key: 'amount',
  },
  {
    title: '预计交货日期',
    width: 150,
    maxLength: 80,
    dataIndex: 'estDeliveryDate',
    key: 'estDeliveryDate',
    align: 'center',
  },
  {
    title: '备注',
    width: 150,
    maxLength: 80,
    dataIndex: 'remark',
    key: 'remark',
  }
];

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 商品选择表格的选择事件
const onProductSelectChange = (keys, rows) => {

  // 使用新数组实例重置选中状态
  selectedProductKeys.value = [...keys];
  selectedProducts.value = [...rows];
};

// 打开商品选择器
const showProductSelector = () => {
  // 每次打开时完全重置选择状态
  selectedProductKeys.value = [];
  selectedProducts.value = [];
  gNameKeyword.value = ''; // 重置关键词

  productSelectorVisible.value = true;
};

// 处理商品查询
const handleSearchProducts = () => {
  // 调用加载商品数据接口，传入商品名称参数
  loadProducts(gNameKeyword.value);
};

// 排序函数：按商品名称排序，再按单价排序（升序）
// 类似于SQL中的 ORDER BY t.goods_name, t.unit_price ASC
const sortTableDataByNameAndPrice = (data) => {
  return [...data].sort((a, b) => {
    // 首先按商品名称排序
    const nameA = a.goodsName || '';
    const nameB = b.goodsName || '';

    // 如果商品名称不同，则按字母顺序排序
    if (nameA !== nameB) {
      return nameA.localeCompare(nameB);
    }

    // 如果商品名称相同，则按单价排序（数字升序）
    const priceA = parseFloat(a.unitPrice || 0);
    const priceB = parseFloat(b.unitPrice || 0);
    return priceA - priceB;
  });
};

// 加载商品数据 - 确保每条数据都有唯一的id
const loadProducts = async (gName = '') => {
  productLoading.value = true;
  try {
    // 创建请求参数对象，包含商品名称
    const params = {
      gName: gName, // 传递商品名称参数
      commonMark:'6'
    };

    // 调用API获取商品信息，传入参数
    const response = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMaterialInformation.matForPlan}`,
      params
    );

    if (response.code === 200) {
      // 确保每个商品项有唯一的 id
      productList.value = (response.data || []).map(item => ({
        ...item,
        id: item.sid || item.gName || `mat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }));
      // 将 gNames 转换为对象数组
      gNames.value = productList.value.map(product => ({
        label: product.gName,
        value: product.gName
      }));
      // 重置选择状态
      selectedProductKeys.value = [];
      selectedProducts.value = [];
    } else {
      message.error(response.message || '获取商品信息失败');
    }
  } catch (error) {
    console.error('加载商品数据失败:', error);
    message.error('获取商品信息失败');
  } finally {
    productLoading.value = false;
  }
};

// 确认选择商品 - 修复重复提示的问题
const handleSelectProducts = () => {
  if (selectedProducts.value.length === 0) {
    message.warning('请至少选择一个商品');
    return;
  }
  // 获取已有表格中的商品名称
  const existingItems = tableData.value.map(item => ({
    goodsName: item.goodsName
  })).filter(item => item.goodsName !== undefined);

  // 检查选中的商品中是否有与已有表格中商品名称相同的项
  var filter = selectedProducts.value.filter(item =>
    existingItems.some(existing =>
      existing.goodsName === item.gName
    )
  );

  if(filter.length > 0){
    // 更新提示消息，显示重复的商品名称
    const duplicateInfo = filter.map(item => `${item.gName}`).join(',');
    message.warning(`商品名称 ${duplicateInfo} 已存在！`);
    return;
  }

  addLoading.value = true;
  let selectedSid = '';

  // 保存选中商品数量用于显示消息
  const addedCount = selectedProducts.value.length;

  // 保存当前表格中的所有数据（深拷贝以避免引用问题）
  const existingData = JSON.parse(JSON.stringify(tableData.value));

  // 创建新记录数组
  const newRecords = [];

  try {
    // 为每个选中的商品创建一行数据
    selectedProducts.value.forEach(product => {
      const id = Date.now().toString() + Math.random().toString(36).substr(2, 5);

      // 将新行添加到跟踪数组中
      newAddedRows.value.push(id);
      selectedSid = id;

      // 从选中商品创建新记录，并明确标记为新增
      const newRecord = {
        id: id,
        goodsName: product.gName || '',
        goodsDesc: product.goodsDesc || '',
        qty: '', // 确保数量为空字符串，而不是0
        unit: product.unit || '',
        unitPrice: product.importUnitPrice || '',
        amount: 0,
        estDeliveryDate: null,
        remark: '',
        goodsCategory: product.merchandiseCategories || '',
        headId: props.headId,
        isNewAdded: true // 明确标记为新增行
      };

      // 添加到新记录数组
      newRecords.push(newRecord);

      // 设置为可编辑状态
      editableData[id] = JSON.parse(JSON.stringify(newRecord));
      // 如果有数量和单价，计算金额
      if (editableData[id].unitPrice !== null && editableData[id].qty !== null && editableData[id].qty !== '') {
        editableData[id].amount = totalCount(editableData[id]);
      }
    });

    // 合并现有数据和新数据
    const combinedData = [...existingData, ...newRecords];

    // 对合并后的数据进行排序
    const sortedData = sortTableDataByNameAndPrice(combinedData);

    // 用排序后的数据替换表格数据
    tableData.value = sortedData;

    // 设置编辑状态
    detailEditStatus.value = editStatus.ADD;

    // 关闭弹窗
    productSelectorVisible.value = false;

    // 显示成功消息
    message.success(`成功添加 ${addedCount} 条商品记录`);

    // 确保表格滚动到底部
    setTimeout(() => {
      const table = tableRef.value;
      if (table && table.scrollTo) {
        table.scrollTo({
          top: table.scrollHeight,
          behavior: 'smooth'
        });
      }

      // 如果有添加的行，选中第一个新添加的行并设置焦点到数量输入框
      if (newRecords.length > 0) {
        // 找到第一个新添加的行
        const firstNewRecord = newRecords[0];
        const firstNewRecordInTable = tableData.value.find(item => item.id === firstNewRecord.id);

        if (firstNewRecordInTable) {
          // 使用setTimeout确保DOM更新后再设置焦点
          setTimeout(() => {
            // 找到该行的数量输入框并设置焦点
            const rowInput = document.querySelector(`[data-row-key="${firstNewRecordInTable.id}"] input[data-field="qty"]`);
            if (rowInput) {
              rowInput.focus();
            }
          }, 200); // 增加延迟时间，确保DOM完全更新
        }
      }
    }, 200); // 增加延迟时间，确保DOM完全更新
  } catch (error) {
    console.error('添加商品记录失败:', error);
    message.error('添加商品记录失败');
  } finally {
    addLoading.value = false;
  }
};

// 现在字段总是可编辑的，不需要检查编辑状态函数

// 添加一个方法来处理商品名称的变化
const handleGNameChange = (id, selectedGName) => {
  const matchedProduct = productList.value.find(product => product.gName === selectedGName);

  if (matchedProduct) {
    // 找到对应的记录
    const record = tableData.value.find(item => item.id === id);
    if (record) {
      // 更新记录中的其他字段
      record.goodsDesc = matchedProduct.goodsDesc || '';
      record.unitPrice = matchedProduct.importUnitPrice || 0;
      record.goodsCategory = matchedProduct.merchandiseCategories && matchedProduct.categoryName
        ? `${matchedProduct.merchandiseCategories} ${matchedProduct.categoryName}`
        : (matchedProduct.merchandiseCategories || '');

      // 重新计算金额（因为单价可能已经变化）
      calculateAmount(record);

      // 强制更新 tableData 以触发视图重新渲染
      tableData.value = [...tableData.value];
    }
  }
};



/* 进口计划表体导入 */
const handlerImport = () => {
  // 判断表头是状态是否是 编制状态
  console.log('props',props.editConfig.editData.status)
  if(props.editConfig.editData.status !== '0'){
    message.warning('仅单据状态为编制的数据允许导入！')
    return
  }
  // 打开导入弹窗
  importShow.value = true;
}
const importSuccess = ()=>{
  importShow.value = false;
  getList()
  // 重新计算汇总数据
  calculateTotals();
}

defineExpose({
  getTableData: () => {
    // 创建一个深拷贝，避免直接修改原始数据
    const result = JSON.parse(JSON.stringify(tableData.value));

    // 遍历所有编辑中的数据，更新到结果中
    Object.keys(editableData).forEach(id => {
      const index = result.findIndex(item => item.id === id);
      if (index !== -1) {
        // 只更新允许编辑的字段
        const editableFields = ['productName', 'productModel','quantity', 'unit', 'unitPrice',  'estDeliveryDate', 'remark', 'amount'];
        editableFields.forEach(field => {
          if (editableData[id][field] !== undefined) {
            result[index][field] = editableData[id][field];
          }
        });
      }
    });

    return result;
  },
  reloadData: getList, // 暴露刷新方法供父组件调用
  saveAllData: () => {
    // 保存所有数据的方法
    return tableData.value;
  }
});
</script>

<style scoped>
.plan-detail-table {
  margin-top: 16px;
}

.cs-pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}

.cs-margin-right {
  margin-right: 16px;
}

.cs-list-total-data {
  margin-right: 16px;
}

.count-number {
  margin-right: 8px;
}
</style>
