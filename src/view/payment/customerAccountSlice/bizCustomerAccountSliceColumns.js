import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }
  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};
const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()
// 初始化时获取数据
await getMerchantOptions()
function getColumns() {
  const commColumns = reactive([
    'sid'
    , 'businessType'
    , 'accountNo'
    , 'contractNo'
    , 'currE'
    , 'currI'
    , 'exchangeRateE'
    , 'exchangeRateI'
    , 'goodsPriceE'
    , 'goodsPriceI'
    , 'agentFeeRate'
    , 'agentFee'
    , 'agentTaxFee'
    , 'agentFeeTotal'
    , 'businessDate'
    , 'gName'
    , 'sendFinance'
    , 'producrSome'
    , 'note'
    , 'freightRatio'
    , 'redFlush'
    , 'status'
    , 'apprStatus'
    , 'confirmTime'
    , 'isConfirm'
    , 'purchaseMark'
    , 'purchaseNoMark'
    , 'goodsPriceERmb'
    , 'goodsPriceIRmb'
    , 'vatRate'
    , 'freightForwardingFee'
    , 'insuranceFee'
    , 'costFee'
    , 'depositReceived'
    , 'refundFee'
    , 'totalAmount'
    , 'customer'
    , 'businessLocation'
  ])
  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])
  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])
  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'businessType',
      resizable: true,
      key: 'businessType',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType2))
      }
    },
    {
      title: '客户',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'customer',
      resizable: true,
      key: 'customer',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '合同号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '结算单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'accountNo',
      resizable: true,
      key: 'accountNo',
    },
    {
      title: '出货单号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'purchaseOrderNo',
      resizable: true,
      key: 'purchaseOrderNo',
    },
    {
      title: '人民币金额',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'totalAmount',
      resizable: true,
      key: 'totalAmount',
    },
    {
      title: '发送财务系统',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'sendFinance',
      resizable: true,
      key: 'sendFinance',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '制单人',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'createrUserName',
      resizable: true,
      key: 'createrUserName',
    },
    {
      title: '制单日期',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'createrTime',
      resizable: true,
      key: 'createrTime',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
    {
      title: '单据状态',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'status',
      resizable: true,
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '确认时间',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
  ])
  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
export { getColumns }
