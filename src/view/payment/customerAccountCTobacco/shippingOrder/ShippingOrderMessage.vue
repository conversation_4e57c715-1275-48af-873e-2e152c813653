<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search" >
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
<!--                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">-->
<!--                  <template #icon>-->
<!--                    <GlobalIcon type="redo" style="color:#fff"/>-->
<!--                  </template>-->
<!--                </a-button>-->
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search" style="padding-bottom: 10px;padding-top: 10px">
            <div v-show="showSearch">
              <ContractSearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 表格区域 -->
<!--                :scroll="{ y: tableHeight,x:400 }"  -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          size="small"
          :height="450"
          column-drag
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
        >
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination  v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
      <!-- 保存按钮 -->
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '90px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <div class="cs-submit-btn merge-3">
            <div style="display: flex;">
              <a-button size="small" :loading="iconLoading" type="primary" @click="handlerSave" class="cs-margin-right">保存</a-button>
              <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            </div>
          </div>
        </a-form>
      </div>
    </div>


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref, watch} from "vue";
import ContractSearch from "./ShippingOrderSearch.vue";
import {getColumns} from "./ShippingOrderColumns";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";

import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import {contractList, contractInsert} from "@/api/cost/cost_message_info";
import {editStatus, productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {message} from "ant-design-vue";
import {insertByShippingCT} from "@/api/payment/payment_info";



/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  onPageChange,
  handleShowSearch,
  // handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  getList,
  ajaxUrl,
  // handlerRefresh,
  gridData,
  doSearch

} = useCommon()



defineOptions({
  name: 'ShippingOrderMessage',
});

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const iconLoading = ref(false);

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','onHeadback']);

const onBack = (val) => {
  emit('onEditBack', val);
};
const returnData = (val) => {
  emit('onHeadback', val);
};

const formData = reactive({

});



const rules = {

};
// const handlerRefresh =()=> {
//   if (headSearch.value) {
//     headSearch.value.resetSearch()
//     page.current = 1
//     page.pageSize = 20
//   }
//   doSearch()
// }
const handlerSearch =()=> {
  if(headSearch.value.searchParam.businessType === '' || headSearch.value.searchParam.businessType === null){
    message.error('请选择业务类型')
    return
  }
  // if('6' === headSearch.value.searchParam.businessType){
  //   ajaxUrl.selectAllPage = ycCsApi.payment.customerAccount.shippingListToCustomerAccount
  //   console.log('6'+ajaxUrl.selectAllPage)
  // }else if ('9' === headSearch.value.searchParam.businessType){
  //   ajaxUrl.selectAllPage = ycCsApi.payment.customerAccount.shippingListToCustomerAccount9
  //   console.log('9'+ajaxUrl.selectAllPage)
  // }
  doSearch(ajaxUrl.selectAllPage, 'post')
}

const typeMessage = ref({
  //币种
  currTypeOptions: [],
  //商品类别
  productTypeOptions: [],
})
const { totalColumns } = getColumns(typeMessage)


const getCurrOptions = async () => {
  try {
    const params = {paramsType: "CURR"}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.baseInfoCustomerParams.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        typeMessage.value.currTypeOptions.push({
          value: item.paramsCode,
          label: item.customParamCode
        });
      });
    } else {
      message.error(res.message || '获取币种类型数据失败');
    }
  } catch (error) {
    message.error('获取币种类型数据失败');
  }
}

const getProductTypeOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.productType.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        typeMessage.value.productTypeOptions.push({
          value: item.categoryCode,
          label: item.categoryName
        });
      });
    } else {
      message.error(res.message || '获取商品类别数据失败');
    }
  } catch (error) {
    message.error('获取商品类别数据失败');
  }
}


/* 显示列数据 */
const showColumns =  ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
onMounted(fn => {
  ajaxUrl.selectAllPage = ycCsApi.payment.customerAccountCTobacco.shippingListToCustomerAccount
  // tableHeight.value = getTableScroll(100,'');
  // getList()
  getCurrOptions()
  getProductTypeOptions()
})
const tableHeight = ref(500)

/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};



/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})

// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      iconLoading.value = true;
      if(gridData.selectedRowKeys.length < 1){
        iconLoading.value = false;
        return message.error('请选择进货明细!')
      }
      if(gridData.selectedRowKeys.length > 1){
        iconLoading.value = false;
        return message.error('只能选择一条进货明细!')
      }
      const contractMessage = {
        sid: gridData.selectedRowKeys[0],
        businessType: headSearch.value.searchParam.businessType,
      };
      insertByShippingCT(contractMessage).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
            // editConfig.value.editStatus = editStatus.EDIT
            // editConfig.value.editData =  res.data
            returnData(res.data)
          }
        })
    })
    .catch(error => {
      console.log('validate failed', error);
    }).finally(() => {
    iconLoading.value = false;
  })
};
</script>

<style lang="less" scoped>
.grid-item {
  margin: 2px; /* 重置外边距 */
  width: 99%;
}
</style>
