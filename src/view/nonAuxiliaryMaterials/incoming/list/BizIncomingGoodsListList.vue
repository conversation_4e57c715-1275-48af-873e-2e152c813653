<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">

        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:add']">
          <a-button size="small" @click="handlerAdd" :disabled="!props.isEdit">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增明细
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:nonAuxiliaryMaterials-incoming-list:delete']">
          <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            删除
          </a-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>

          <!-- 行内编辑模板 -->
          <template #bodyCell="{text, record, index, column, key }">
            <template v-if="(!props.showDisable) && column.dataIndex === 'inQuantity'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].inQuantity"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>

            <template v-if="(!props.showDisable) && column.dataIndex === 'unitPrice'">
              <a-input-number
                v-if="props.isEdit === true"
                size="small"
                v-model:value="dataSourceList[index].unitPrice"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
                :precision="6"
              />
              <span v-else>{{formatSpecifiedNumber(text,true,2)}}</span>
            </template>


          </template>
        </s-table>
      </div>

      <product-select-modal
        v-model:visible="productModalVisible"
        :contract-no="props.formData.contractNo"
        :unit-options="unitList"
        @select="handleProductSelect"
      />

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="cs-margin-right cs-list-total-data ">
          数量：{{formatNumberNew(totalData.inQuantity)}} ，金额：{{formatNumberNew(totalData.amount)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 维护进口发票号 -->
    <EditInvoiceNumberDialog
        :selected-ids="selectedIds"
        :edit-config="editConfig"
        @onEditBack="handleBack"
        @success="handleSuccess"
        v-model:open="editInvoiceNumberDialogVisible"
    />


    <!-- 进口发票号汇总 -->
    <cs-modal :visible="isShowInvoiceTotalDialog" :title="'发票汇总显示'" :width="1000" :footer="true" @cancel="handleCancelInvoiceTotalDialog">
      <template #customContent>
        <incoming-invoice-total-dialog
          :head-id="props.headId"
        />

      </template>
      <template #footer>
        <div style="display: flex;justify-content: right;align-items: center">
          <a-button @click="handleCancelInvoiceTotalDialog" size="small">返回</a-button>
        </div>
      </template>
    </cs-modal>



  </section>


</template>

<script setup>
  /* 使用自定义 Hook 函数 */
  import {useCommon} from '@/view/common/useCommon'
  import {createVNode, onMounted, reactive, ref, watch} from "vue";
  import ycCsApi from "@/api/ycCsApi";
  import {message, Modal} from "ant-design-vue";
  import EditInvoiceNumberDialog from "@/view/nonAuxiliaryMaterials/incoming/componment/EditInvoiceNumberDialog.vue";
  import IncomingInvoiceTotalDialog from "@/view/nonAuxiliaryMaterials/incoming/componment/IncomingInvoiceTotalDialog.vue";
  import CsModal from "@/components/modal/cs-modal.vue";
  import {useColumnsRender} from "@/view/common/useColumnsRender";
  import {isNullOrEmpty} from "@/view/utils/common";
  import {getNonInComingListBySid, updateInComingList} from "@/api/cs_api_constant";
  import useEventBus from "@/view/common/eventBus";

  const { inputFormatter, inputParser,formatNumberNew,formatSpecifiedNumber,cmbShowRender } = useColumnsRender()


  import {baseColumns} from "@/view/common/baseColumns";
  import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
  import {GlobalIcon} from "@/components/icon";
  import ProductSelectModal from "@/view/nonAuxiliaryMaterials/incoming/componment/ProductSelectModal.vue";
  import {addContractList} from "@/api/auxiliaryMaterials/forContract/contractApi";
  const {baseColumnsExport, baseColumnsShow} = baseColumns()



  const { onEvent } = useEventBus()
  /* 定义接收数据  */
  const props = defineProps({
    /* 表头headId */
    headId: {
      type: String,
      default: () => ''
    },
    formData: {
      type: Object,
      default: () => {}
    },
    /* 是否能编辑 */
    isEdit: {
      type: Boolean,
      default: () => true
    },
    /* 是否查看模式 */
    showDisable:{
      type: Boolean,
      default: () => false
    }
  });


  /* 引入通用方法 */
  const {
    editConfig,
    show,
    page,
    showSearch,
    headSearch,
    operationEdit,
    handleShowSearch,
    handlerSearch,
    dataSourceList,
    tableLoading,
    getTableScroll,
    exportLoading,
    ajaxUrl,
    handlerRefresh

  } = useCommon()



  defineOptions({
    name: 'BizIncomingGoodsList',
  });




  /* 引入表单数据 */
  const gridData = reactive({
    selectedRowKeys: [],
    selectedData:[],
    loading: false,
  });



  /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
  const onSelectChange = (selectedRowKeys, rowSelectData) => {
    gridData.selectedData = rowSelectData;
    gridData.selectedRowKeys = selectedRowKeys;
  };



  /* 获取列表 */
  // 方法定义
  const getList = async (val) => {
    let headId = await  val?val:props.headId
    // console.log('表头HeadId',headId1)
    tableLoading.value = true
    let params = {
      headId: headId
    }
    try {
      const res =  await window.majesty.httpUtil.postAction(`${ycCsApi.bizNonInComingList.list}?page=${page.current}&limit=${page.pageSize}`,
        params
      );
      dataSourceList.value = res.data
      page.total = res.total
    }catch(err) {

      message.error(err.message)
    }finally {
      tableLoading.value = false
    }

  }


  const onPageChange = async (pageNumber, pageSize) =>{
    page.current = pageNumber
    page.pageSize = pageSize
    // 在这里添加处理页码变化的逻辑
    await getList()
    await getListSumTotal()
  }


  const productModalVisible = ref(false)

  const handlerAdd = () => {
    productModalVisible.value = true
  }

  const deleteLoading = ref(false)
  /* 删除数据 */
  const handlerDelete = () => {
    if (gridData.selectedRowKeys.length <= 0){
      message.warning('请选择一条数据')
      return
    }
    // 只允许操作一票数据
    if (gridData.selectedRowKeys.length > 1){
      message.warning('只允许操作一票数据')
      return
    }
    // 弹出确认框
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk() {
        deleteLoading.value = true
        window.majesty.httpUtil.deleteAction(`${ycCsApi.bizNonInComingList.delete}/${gridData.selectedRowKeys[0]}`).then(res => {
          if (res.code === 200) {
            message.success("删除成功！")
            getList()
            getListSumTotal()
          } else {
            message.error(res.message)
          }
        }).finally(() => {
          deleteLoading.value = false
        })
      },
      onCancel() {

      },
    });

  }

  const handleProductSelect= (selectedProducts) =>  {
    try {
      console.log(selectedProducts)
      let params = {
        headId: props.headId,
        sids: selectedProducts
      }
      window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingList.batchInsert, params).then((res)=>{
        if (res.code === 200){
          message.success('新增成功!')
          productModalVisible.value = false;
          // 刷新列表和汇总数据
          getList();
          getListSumTotal();
        } else {
          message.error(res.message);
        }
      })
    } catch (error) {
      console.error('添加商品失败', error);
      message.error('添加商品失败，请重试');
    }
  }

  /* 维护进口发票号 */
  const editInvoiceNumberDialogVisible = ref(false)
  const selectedIds = ref([])


  const handleBack = (val) => {
    // 处理返回逻辑
    gridData.selectedRowKeys = [];
    gridData.selectedData = [];
  }

  const handleSuccess = () => {
    // 处理保存成功后的逻辑
    getList()
    getListSumTotal()
    gridData.selectedRowKeys = [];
    gridData.selectedData = [];
  }
  const handleOpenEditInvoiceNumberDialog = () => {

    if (gridData.selectedRowKeys.length === 0) {
      message.warning('请选择要维护的行')
      return
    }
    editInvoiceNumberDialogVisible.value = true
    selectedIds.value = gridData.selectedRowKeys
  }

  const handleEditInvoiceNumberSuccess = () => {
    getList()
    getListSumTotal()
  }


const sendEntry= () => {
    // 单据状态为"确认"数据可以操作发送，如不符合，则提示用户"数据条件不符，请重新选择"
    if (props.formData.dataState !== '1'){
      message.warning('数据条件不符，请重新选择')
      return
    }

    // A判断表头"发送报关"栏位值如果为1否的，系统弹框提"是否将数据发送关务系统？"
    if(props.formData.sendEntry === '1')
    {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '是否将数据发送关务系统？',
        onOk() {
          let headId = props.headId

          let params = {
            id: headId
          }
          window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.sendEntry, params).then(res=>{
            if (res.code === 200) {
              message.success("发送成功！")
            } else {
              message.error(res.message)
            }
          })

        },
        onCancel() {
          // 点击取消则关闭系统弹框，不执行发送及不修改相关栏位值
        },
      });
    }
    // B判断表头"发送报关"栏位值如果为0是的，提示用户"是否再次发送报关"
    else {
      Modal.confirm({
        title: '提醒',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确认',
        cancelText: '取消',
        content: '是否再次发送报关？',
        onOk() {
          let headId = props.headId

          let params = {
            id: headId
          }
          window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.sendEntry, params).then(res=>{
            if (res.code === 200) {
              message.success("发送成功！")
            } else {
              message.error(res.message)
            }
          })

        },
        onCancel() {
          // 点击取消则关闭系统弹框，不执行发送及不修改相关栏位值
        },
      });
    }





}

  const isShowInvoiceTotalDialog = ref(false)
  const handleShowInvoiceTotalDialog = () => {
    isShowInvoiceTotalDialog.value = true
  }
  const handleCancelInvoiceTotalDialog = () => {
    isShowInvoiceTotalDialog.value = false
  }


  const isEditLoading = ref(false)
  const handleQuantityChange = async (record, column) => {
    if (isEditLoading.value === true) {
      console.log('回车，失焦同时触发！');
      return;
    }

    isEditLoading.value = true;

    if (!record || !record.id) {
      isEditLoading.value = false;
      return;
    }

    try {
      const res = await getNonInComingListBySid(record.id, record);

      if (res.code !== 200) {
        isEditLoading.value = false;
        return;
      }

      const dataTemp = res.data;

      if (column.dataIndex === 'inQuantity') {
        if (isNullOrEmpty(dataTemp) || dataTemp.inQuantity === record.inQuantity) {
          isEditLoading.value = false;
          return;
        }
        // tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(
          `${ycCsApi.bizNonInComingList.updateInQuality}`,
          record
        );

        if (updateRes.code === 200) {
          // message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          // tableLoading.value = false
        } else {
          record.inQuantity = dataTemp.inQuantity; // 恢复原值
          message.error(updateRes.message);
          // tableLoading.value = false
        }
      }


      if (column.dataIndex === 'quantity') {
        if (isNullOrEmpty(dataTemp) || dataTemp.quantity === record.quantity) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(
          `${ycCsApi.bizNonInComingList.updateQuantity}`,
          record
        );

        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
        } else {
          record.quantity = dataTemp.quantity; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


      if (column.dataIndex === 'unitPrice') {
        if (isNullOrEmpty(dataTemp) || dataTemp.unitPrice === record.unitPrice) {
          isEditLoading.value = false;
          return;
        }
        // tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(
          `${ycCsApi.bizNonInComingList.updateAmount}`,
          record
        );

        if (updateRes.code === 200) {
          // message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          // tableLoading.value = false
        } else {
          record.unitPrice = dataTemp.unitPrice; // 恢复原值
          message.error(updateRes.message);
          // tableLoading.value = false
        }
      }


      if (column.dataIndex === 'invoiceNo') {
        if (isNullOrEmpty(dataTemp) || dataTemp.invoiceNo === record.invoiceNo) {
          isEditLoading.value = false;
          return;
        }
        tableLoading.value = true
        const updateRes = await window.majesty.httpUtil.postAction(
          `${ycCsApi.bizNonInComingList.updateInvoiceNo}`,
          record
        );

        if (updateRes.code === 200) {
          message.success("修改成功！");
          Object.assign(record, updateRes.data); // 更新 record 数据
          tableLoading.value = false
        } else {
          record.updateInvoiceNo = dataTemp.updateInvoiceNo; // 恢复原值
          message.error(updateRes.message);
          tableLoading.value = false
        }
      }


    } catch (error) {
      record.inQuantity = dataTemp?.inQuantity; // 出错恢复原值
      message.error(error.message);
      console.error("数量变更异常：", error);
    } finally {
      setTimeout(() => {
        isEditLoading.value = false;
        getListSumTotal()
      }, 100);
    }
  };




  // 获取表体汇总数据
  const totalData = ref({
    inQuantity: 0,
    quantity: 0,
    amount: 0
  })

  const getListSumTotal = async (val) => {
    try {
      let headId = await  val?val:props.headId
      let params = {
        headId: headId
      }
      if (isNullOrEmpty(headId)) {
        return
      }

      const res = await window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingList.getSumTotalByHeadId, params);
      if (res.code === 200) {
        totalData.value = res.data;
      } else {
        message.error(res.message)
      }
    } catch (error) {
      message.error(error.message)
    }
  }









  const unitList = ref([])
  const getUnitList = async () => {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizInComingHead.getUnitList,{})
    if (res.code === 200) {
      unitList.value = res.data
    }
    // unitList.value = res.data
  }

  const currList = ref([])
  const getCurrList = async () => {
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizInComingHead.getCurrList,{})
    if (res.code === 200) {
      // console.log('获取币制列表',res.data)
      currList.value = res.data
    }
    // currList.value = res.data
  }


  const commColumns = reactive([
    'id',
    'businessType',
    'dataState',
    'versionNo',
    'tradeCode',
    'sysOrgCode',
    'parentId',
    'createBy',
    'createTime',
    'updateBy',
    'updateTime',
    'insertUserName',
    'updateUserName',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'goodsName',
    'productModel',
    'quantity',
    'unit',
    'unitPrice',
    'amount',
    'deliveryDate',
    'totalUsd',
    'remarks',
    'headId',
    'inQuantity',
    'inUnit',
    'curr',
    'invoiceNo'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 180,
      align: 'center',
      dataIndex: 'goodsName',
      key: 'goodsName',
      autoHeight: true,
      resizable: true
    },
    {
      title: '商品描述',
      width: 200,
      align: 'center',
      dataIndex: 'productModel',
      key: 'productModel',
      autoHeight: true,
      resizable: true
    },
    {
      title: '数量',
      width: 120,
      align: 'center',
      dataIndex: 'inQuantity',
      key: 'inQuantity',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    // {
    //   title: '进口单位',
    //   width: 120,
    //   align: 'center',
    //   dataIndex: 'inUnit',
    //   key: 'inUnit',
    //   autoHeight: true,
    //   resizable: true,
    //   customRender: ({ text }) => {
    //     return cmbShowRender(text,unitList.value)
    //   }
    // },
    // {
    //   title: '数量',
    //   width: 120,
    //   align: 'center',
    //   dataIndex: 'quantity',
    //   key: 'quantity',
    //   editable: 'cellEditorSlot',
    //   autoHeight: true,
    //   resizable: true,
    //   customRender: ({ text }) => {
    //     return formatSpecifiedNumber(text,true,2)
    //   }
    // },
    {
      title: '单位',
      width: 120,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return cmbShowRender(text,unitList.value)
      }
    },
    // {
    //   title: '币种',
    //   width: 150,
    //   align: 'center',
    //   dataIndex: 'curr',
    //   key: 'curr',
    //   autoHeight: true,
    //   resizable: true,
    //   customRender: ({ text }) => {
    //     return cmbShowRender(text,currList.value)
    //   }
    // },
    {
      title: '单价',
      width: 120,
      align: 'center',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '金额',
      width: 180,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      // editable: 'cellEditorSlot',
      autoHeight: true,
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    // {
    //   title: '进口发票号',
    //   width: 130,
    //   align: 'center',
    //   dataIndex: 'invoiceNo',
    //   key: 'invoiceNo',
    //   editable: 'cellEditorSlot',
    //   autoHeight: true,
    //   resizable: true
    // }

  ])

  // 将方法暴露给父组件
  defineExpose({
    getList,
    handleEditInvoiceNumberSuccess,
    dataSourceList,
    isEditLoading,
  });

  onMounted(() => {

    getList()
    getListSumTotal()
     getUnitList()
     getCurrList()
    // 刷新表体数据
    onEvent('refreshIncomingGoodsList',(headId)=>{
      // console.log('传入headID',headId,'刷新表体数据')
      // props.headId = headId
      getList()
      getListSumTotal()
    })


  })





</script>

<style lang="less" scoped>


</style>
