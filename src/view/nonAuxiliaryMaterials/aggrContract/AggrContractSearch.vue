<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

    <!-- 数据状态 -->
    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 购销合同号 -->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>
    <!-- 供应商 -->
    <a-form-item name="supplier" :label="'供应商'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.supplier" id="supplier">
        <a-select-option v-for="item in supplierOptions" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 国内委托方 -->
    <a-form-item name="domesticPrincipal" :label="'国内委托方'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.domesticPrincipal" id="domesticPrincipal">
        <a-select-option v-for="item in domesticOptions" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeFrom"
              id="createTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder="制单日期止"
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>



  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";

defineOptions({
  name: 'BuyContractSearch'
})
const searchParam = reactive({
  contractNo: '',
  contractYear: '',
  createTimeFrom: '',
  createTimeTo: '',
  status: '',
  supplier: '',
  domesticPrincipal: ''
})

const locale = {
}

//基础资料-客商信息
const supplierOptions = reactive([])
const domesticOptions = reactive([])

const getSupplierOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.getForAggrSupplierSearch}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        supplierOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}
const getDomesticOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.getForAggrDomesticSearch}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        domesticOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}

// 组件挂载时获取客商数据
onMounted(() => {
  getSupplierOptions();
  getDomesticOptions();
})

defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
