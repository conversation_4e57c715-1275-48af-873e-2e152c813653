<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <base-head-search ref="headSearch" :merchandiseCategoriesMap="merchandiseCategoriesMap"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:materialInformation:add']">
            <a-button size="small" @click="handlerAdd2" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:materialInformation:update']">
            <a-button  size="small"  @click="handlerEdit">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.update')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:materialInformation:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:materialInformation:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item"  v-has="['yc-cs:materialInformation:import']">
            <a-button  size="small"  @click="handlerImport">
              <template #icon>
                <GlobalIcon type="file-excel" style="color:deepskyblue"/>
              </template>
              {{localeContent('m.common.button.import')}}
            </a-button>
          </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:materialInformation:cancellation']">
          <a-button  size="small"  @click="handlerCancellation">
            <template #icon>
              <GlobalIcon type="frown" style="color:deeppink"/>
            </template>
            作废
          </a-button>
        </div>


        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>





      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, text, record }">

            <template v-if="commColumnsInput.includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-input
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="margin: -5px 0"
                />
                  </span>
                <span v-else>
                  {{ text }}
                </span>
              </div>
            </template>

            <template v-if="['importUnitPrice'].includes(column.dataIndex)">
<!--            <template v-if="['importUnitPrice','taxExclusive'].includes(column.dataIndex)">-->
              <div>
                <span v-if="editableData[record.sid]">
                <a-input-number
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="margin: -5px 0"
                  :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'"
                  :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                />
                  </span>
                <span v-else>
                  {{ formatNumber(text) }}
                </span>
              </div>
            </template>

            <template v-if="['includingTax'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                     :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'"
                     :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                     @input="onInputChange(record.sid)"
                   />
              </span>
                <span v-else>
                    {{ formatNumber(text) }}
                </span>
              </div>
            </template>
            <template v-if="['taxRate'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                     @input="onInputChange(record.sid)"
                   />
              </span>
                <span v-else-if="text !== null">
                    {{ text + "%" }}
                </span>
                <span v-else>
                    {{ text }}
                </span>
              </div>
            </template>

            <template v-if="['supplierDiscountRate'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                   />
              </span>
                <span v-else-if="text !== null">
                    {{ text + "%" }}
                </span>
                <span v-else>
                    {{ text }}
                </span>
              </div>
            </template>

            <template v-if="['priceExcludingTax'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                  <a-input v-model:value="editableData[record.sid][column.dataIndex]" style="width: 100%" :disabled=true
                           :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'"
                           :parser="value => value.replace(/\$\s?|(,*)/g, '')"></a-input>
                </span>
                <span v-else>
                 {{ formatNumber(text) }}
                </span>
              </div>
            </template>

            <template v-if="['supplierCode'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="supplierCodeMap"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                 {{ cmbShowRender(text,supplierCodeMap) }}
                </span>
              </div>
            </template>


            <template v-if="['merchandiseCategories'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="merchandiseCategoriesMap"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                 {{ cmbShowRender(text,merchandiseCategoriesMap) }}
                </span>
              </div>
            </template>

            <template v-if="['commonMarkList'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-select v-if="editableData[record.sid]"
                             v-model:value="editableData[record.sid][column.dataIndex]"
                             mode="multiple"
                             style="width: 100%"
                             placeholder="Please select"
                             :options="commonMarkMap"
                             @change="handleCommonMarkChange"
                   ></a-select>
                </span>
                <span v-else>
                 {{ arrayToCmbShowRender(text,commonMarkMap) }}
                </span>
              </div>
            </template>



            <template v-if="['packagingInformation'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="packagingInformationMap"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                  {{ cmbShowRender(text,packagingInformationMap) }}
<!--                 {{ text }}-->
                </span>
              </div>
            </template>


            <template v-if="['dataState'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="productClassify.dataStatus"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                  {{ cmbShowRender(text,productClassify.dataStatus) }}
                </span>
              </div>
            </template>

            <template v-if="['nameMethod'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="productClassify.nameMethodType"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                  {{ cmbShowRender(text,productClassify.nameMethodType) }}
                </span>
              </div>
            </template>


            <template v-if="['curr'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="currMap"
                  @change="handleChange"
                ></a-select>
              </span>
                <span v-else>
                  {{ text }}
                </span>
              </div>
            </template>

            <template v-else-if="column.dataIndex === 'operation'">
              <div class="operation-container">
                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleEditByRow(record)"
                    :style="operationEdit('edit')"
                  >
                    <template #icon>
                      <GlobalIcon type="form" style="color:#e93f41"/>
                    </template>
                  </a-button>
                </div>

                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>
              </div>
<!--              <div class="editable-row-operations">-->
<!--                <span v-if="editableData[record.sid]">-->
<!--                  <a @click="save(record.sid)" style="margin-right: 8px">保存</a>-->
<!--      &lt;!&ndash;            <a-typography-link @click="save(record.sid)">保存 </a-typography-link>&ndash;&gt;-->
<!--                   <a @click="cancel(record.sid)">取消</a>-->
<!--                </span>-->
<!--                <span v-else>-->
<!--&lt;!&ndash;            <a @click="edit(record.sid)">编辑</a>&ndash;&gt;-->
<!--                <a @click="handlerEdit">编辑</a>-->
<!--              </span>-->
<!--              </div>-->
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <base-head-edit :editConfig="editConfig" @onEditBack="handlerOnBack"/>
    </div>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, h, onMounted, provide, reactive, ref, watch} from "vue";
import BaseHeadSearch from "@/view/base/bi-biMaterialInformation/MaterialInformationHeadSearch.vue";
import {getColumns} from "@/view/base/bi-biMaterialInformation/MaterialInformationHeadColumns";
import BaseHeadEdit from "@/view/base/bi-biMaterialInformation/MaterialInformationHeadEdit.vue";
import {message, Modal, Tag} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {MaterialInformationCancelClient, MaterialInformationDeleteClient} from "@/api/bi/bi_client_info";
const {  commColumnsInput} = getColumns()
const {  excelColumnsConfig} = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "../../utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
let { importConfig } = useImport()
import {MaterialInformationInsertClient, MaterialInformationUpdateClient,getMerchantCodeValueClient} from "@/api/bi/bi_client_info";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {productClassify} from "../../common/constant";
const { cmbShowRender } = useColumnsRender()
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import VueMultiselect from "vue-multiselect";

import MultiSelectDropdown from './MultiSelectDropdown.vue';
import {usePCode} from "@/view/common/usePCode";
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()


// table表格字段设置
const totalColumns = ref([
])
// const { totalColumns } = getColumns()

defineOptions({
  name: 'MaterialInformationHeadList',
});



const importShow = ref(false)

const supplierCodeMap = ref([

])
const packagingInformationMap = ref([

])
const currMap = ref([

])
const merchandiseCategoriesMap = ref([

])
const commonMarkMap = ref([
  {
    label: "国营贸易进口卷烟",
    value: "1"
  },
  {
    label: "国营贸易进口辅料",
    value: "2"
  },
  {
    label: "国营贸易进口烟机设备",
    value: "3"
  },
  {
    label: "国营贸易进口丝束",
    value: "4"
  },
  {
    label: "国营贸易内购内销丝束",
    value: "5"
  },
  {
    label: "非国营贸易进口辅料",
    value: "6"
  },
  {
    label: "出料加工进口薄片",
    value: "7"
  },
  {
    label: "出口烟机设备",
    value: "8"
  },
  {
    label: "出口辅料",
    value: "9"
  }
])

const { getPCode } = usePCode();
const pCode = ref('');


onMounted(fn => {

  ajaxUrl.selectAllPage = ycCsApi.bizMaterialInformation.list
  ajaxUrl.exportUrl = ycCsApi.bizMaterialInformation.export

  tableHeight.value = getTableScroll(100,'');

  totalColumns.value = [
    {
      width: 120,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      visible: 'true',
      resizable:"true",
    },
    {
      title: '商品名称',
      width: 150,
      align: 'center',
      dataIndex: 'gName',
      key: 'gName',
      resizable:"true",
    },
    {
      title: '中文简称',
      width: 220,
      align: 'center',
      dataIndex: 'shortCn',
      key: 'shortCn',
      resizable:"true",
    },
    {
      title: '开票名称',
      width: 220,
      align: 'center',
      dataIndex: 'billingName',
      key: 'billingName',
      resizable:"true",
    },
    {
      title: '英文全称',
      width: 220,
      align: 'center',
      dataIndex: 'fullEnName',
      key: 'fullEnName',
      resizable:"true",
    },
    {
      title: '英文简称',
      width: 150,
      align: 'center',
      dataIndex: 'shortEnName',
      key: 'shortEnName',
      resizable:"true",
    },
    {
      title: '商品类别',
      width: 150,
      align: 'center',
      dataIndex: 'merchandiseCategories',
      key: 'merchandiseCategories',
      resizable:"true",
    },
    {
      title: '供应商',
      width: 150,
      align: 'center',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      resizable:"true",
    },
    {
      title: '供应商折扣率',
      width: 150,
      align: 'center',
      dataIndex: 'supplierDiscountRate',
      key: 'supplierDiscountRate',
      resizable:"true",
    },
    {
      title: '进口单价',
      width: 150,
      align: 'center',
      dataIndex: 'importUnitPrice',
      key: 'importUnitPrice',
      resizable:"true",
    },
    {
      title: '币种',
      width: 150,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      resizable:"true",
    },
    {
      title: '国家产品目录',
      width: 150,
      align: 'center',
      dataIndex: 'nationalProductCatalogue',
      key: 'nationalProductCatalogue',
      resizable:"true",
    },
    {
      title: '条形码',
      width: 150,
      align: 'center',
      dataIndex: 'barCode',
      key: 'barCode',
      resizable:"true",
    },
    {
      title: '常用标志',
      minWidth: 680,
      align: 'center',
      dataIndex: 'commonMarkList',
      key: 'commonMarkList',
      resizable:"true",
      // customRender: ({ text }) => {
      //   return h(<div></div>, cmbShowRender(text,commonMarkMap))
      // }
    },
    {
      title: '包装信息',
      width: 150,
      align: 'center',
      dataIndex: 'packagingInformation',
      key: 'packagingInformation',
      resizable:"true",
    },
    {
      title: '中烟MIS编码',
      width: 150,
      align: 'center',
      dataIndex: 'misCode',
      key: 'misCode',
      resizable:"true",
    },
    {
      title: '统计名称',
      width: 150,
      align: 'center',
      dataIndex: 'statisticalName',
      key: 'statisticalName',
      resizable:"true",
    },
    {
      title: '报送税务总局牌号名称方式',
      width: 300,
      align: 'center',
      dataIndex: 'nameMethod',
      key: 'nameMethod',
      resizable:"true",
    },
    // {
    //   title: '国内不含税调拨价（RMB）',
    //   width: 300,
    //   align: 'center',
    //   dataIndex: 'taxExclusive',
    //   key: 'taxExclusive',
    //   resizable:"true",
    // },
    {
      // title: '含税单价',
      title: '国内含税调拨价(RMB)',
      width: 150,
      align: 'center',
      dataIndex: 'includingTax',
      key: 'includingTax',
      resizable:"true",
    },
    {
      title: '税率',
      width: 150,
      align: 'center',
      dataIndex: 'taxRate',
      key: 'taxRate',
      resizable:"true",
    },
    {
      // title: '不含税单价',
      title: '国内不含税调拨价(RMB)',
      width: 150,
      align: 'center',
      dataIndex: 'priceExcludingTax',
      key: 'priceExcludingTax',
      resizable:"true",
    },
    {
      title: '备注',
      width: 150,
      align: 'center',
      dataIndex: 'note',
      key: 'note',
      resizable:"true",
    },
    {
      title: '创建人',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertUserName',
      key: 'insertUserName',
      resizable:"true",
    },
    {
      title: '创建日期',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertTime',
      key: 'insertTime',
      resizable:"true",
    },
    {
      title: '数据状态',
      width: 150,
      align: 'center',
      dataIndex: 'dataState',
      key: 'dataState',
      resizable:"true",
      /*            customRender: ({ text }) => {
                    return h(Tag, cmbShowRender(text,productClassify.dataStatus))
                  }*/
    },
  ]
  getPCode().then(res => {
    console.log('res', res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      // label: `${value} ${label}`,
      value
    }));
  });

    getMerchantCodeValueClient().then((res)=>{
      if (res.code === 200){
        //供应商
        if (typeof(res.data.supplierCodeMap) !== "undefined"){
          res.data.supplierCodeMap.map(item => {
            supplierCodeMap.value.push({
              label: item.label,
              value: item.value
            })
          })
        }

        //包装信息
        if (typeof(res.data.packagingInformation) !== "undefined"){
            res.data.packagingInformation.map(item => {
              packagingInformationMap.value.push({
                label: item.value,
                value: item.label
              })
            })
        }
        //商品类别
        if (typeof(res.data.merchandiseCategories) !== "undefined"){
          res.data.merchandiseCategories.map(item => {
            merchandiseCategoriesMap.value.push({
              label: item.value,
              value: item.label
            })
          })
        }
        //币种
        // if (typeof(res.data.currList) !== "undefined"){
        //     res.data.currList.map(item => {
        //       currMap.value.push({
        //         label: item.label,
        //         value: item.value
        //       })
        //     })
        // }

      }
    }).finally(()=>{
      getList()
    })
  initCustomColumn()
  if(window.fuyun){
    window.fuyun.majesty.util.handleAsyncImport({id:'1912436446929629186',arrangeId:'1912435101417877505'}).then(res=>{
      console.log('----------------------- 异步导入配置 ------------------------',res)
      importConfig = res
      console.log('----------------------- 异步导入配置 ------------------------',importConfig)
    })
  }
})

const tableHeight = ref('')




/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)



/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  if (flag){
    getList()
  }
}

/* 新增数据 */

const handlerAdd2 = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  // editConfig.value.merchandiseCategoriesMap = merchandiseCategoriesMap
  // editConfig.value.packagingInformationMap = packagingInformationMap
  // editConfig.value.currMap = currMap
  // editConfig.value.supplierCodeMap = supplierCodeMap
  show.value = !show.value;
}



/* 编辑数据 */





/* 打开导入 */
const handlerImport = ()=>{
  importShow.value = !importShow.value
  // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
  // importConfig.taskCode = 'base_client_import'
}


/* 导入成功后事件 */
const importSuccess = ()=>{
  importShow.value =!importShow.value
  getList()
}
// 定义格式化封装函数
function formaData(timer) {
  const year = timer.getFullYear()
  const month = timer.getMonth() + 1 // 由于月份从0开始，因此需加1
  const day = timer.getDate()
  const hour = timer.getHours()
  const minute = timer.getMinutes()
  const second = timer.getSeconds()
  return `${pad(year, 4)}${pad(month)}${pad(day)}${pad(hour)}${pad(minute)}${pad(second)}`
}
// 定义具体处理标准
// timeEl 传递过来具体的数值：年月日时分秒
// total 字符串总长度 默认值为2
// str 补充元素 默认值为"0"
function pad(timeEl, total = 2, str = '0') {
  return timeEl.toString().padStart(total, str)
}


/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`物料信息${timestamp}.xlsx`, totalColumns)
  // let dateW = new Date()
  // doExport('物料信息'+formaData(dateW)+'.xlsx',totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  console.log('temp',tempColumns)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})
// watch(totalColumns.value, (newValue, oldValue) => {
//   if(!window.$vueApp){
//     showColumns.value = [...totalColumns.value];
//   }else {
//     if (newValue.length === 0) {
//       showColumns.value = [...totalColumns.value];
//     }else {
//       showColumns.value = newValue.map((item) => {
//         item.visible = true;
//         return item;
//       })
//       totalColumns.value = newValue.map((item) => {
//         item.visible = true;
//         return item;
//       })
//     }
//   }
// },{immediate:true,deep:true})


const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0';
  }
  // 使用 toLocaleString 添加千位分隔符
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 6
  });
};
const taxCount = (row) => {
  const taxRate = parseFloat(row.taxRate);
  const includingTax = parseFloat(row.includingTax);
  const priceExcludingTax = roundToDecimal(includingTax/(100+taxRate)*100,6)
  return priceExcludingTax !== null ? priceExcludingTax : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}
const editableData = reactive({});
const editSid = ref();
const edit = (sid) => {
  editSid.value = sid
  editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
};
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]
  // editConfig.value.merchandiseCategoriesMap = merchandiseCategoriesMap
  // editConfig.value.packagingInformationMap = packagingInformationMap
  // editConfig.value.currMap = currMap
  // editConfig.value.supplierCodeMap = supplierCodeMap
  // editableData[gridData.selectedData[0].sid] =  gridData.selectedData[0]

  show.value =!show.value;
}

const onInputChange = (sid) =>{
  if(editableData[sid].priceExcludingTax !== null){
    delete editableData[sid].priceExcludingTax;
  }
  if(editableData[sid].taxRate !== null && editableData[sid].includingTax !== null){
    editableData[sid].priceExcludingTax = taxCount(editableData[sid])
  }
}

// 保存操作
const save = (sid) => {
  // 1. 找到数据源中与当前 sid 匹配的项
  const targetItem = dataSourceList.value.find(item => item.sid === sid);

  // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
  Object.assign(targetItem, editableData[sid]);

  // 3. 删除临时编辑的数据，清理内存
  delete editableData[sid];
  if (sid.includes("save")){
    //执行保存请求
    MaterialInformationInsertClient(targetItem).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
        getList()
      } else {
        message.error(res.message)
        edit(sid)
      }
    })
  }else {
    //执行保存请求
    MaterialInformationUpdateClient(sid,targetItem).then((res)=>{
      if (res.code === 200){
        message.success('修改成功!')
        getList()
      } else {
        message.error(res.message)
        edit(sid)
      }
    })
  }


};
// 取消操作
const cancel = (sid) => {
  delete editableData[sid];
  getList()
};
/* 新增数据 */
const handlerAdd = () => {
  let sid = new Date().getTime()+"save";
  const newData = {
    sid: `${sid}`,
    paramsType:"CURR",
    paramsCode:"",
    paramsName:"",
    customParamCode:"",
    customParamName:"",
    dataState:"0",
    taxRate:"13"
  };
  // dataSourceList.value.push(newData);
  dataSourceList.value.unshift(newData);
  dataSourceList.value = [].concat(dataSourceList.value);
  editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
};




/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      MaterialInformationDeleteClient(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}



const handlerCancellation = (value) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      MaterialInformationCancelClient(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("作废成功！")
          getList()
        }
      })
    },
  });
};

const handleChange = (value) => {
  console.log(`selected ${value}`);
};

const handleCommonMarkChange = (value) => {
  editableData[editSid.value].commonMark=value.value
  console.log(`selected ${value}`);
};

const arrayToCmbShowRender = (textArray,commonMarkMap) => {
  let stringR = '';
  setTimeout(()=>{},300)
  if(textArray !== null && textArray !== undefined){
    textArray.forEach(item => {
      stringR = stringR +','+ cmbShowRender(item,commonMarkMap)
    })
    return stringR.substring(1);
  }

};

</script>

<style lang="less" scoped>


</style>
