<template>
  <a-modal
    :visible="visible"
    @update:visible="handleVisibleChange"
    title="复制购销合同"
    :width="500"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-form
      ref="formRef"
      labelAlign="right"
      :label-col="{ style: { width: '120px' } }"
      :model="formData"
      :rules="rules"
    >
      <!-- 购销合同号 -->
      <a-form-item name="contractNo" label="购销合同号" :colon="false">
        <a-input 
          size="small" 
          v-model:value="formData.contractNo" 
          placeholder="请输入购销合同号"
          style="width: 100%" 
        />
      </a-form-item>
    </a-form>
    
    <div class="modal-footer" style="text-align: right; margin-top: 20px;">
      <a-button @click="handleCancel" style="margin-right: 8px;">取消</a-button>
      <a-button @click="handleConfirm" type="primary" :loading="loading">确定</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  contractNo: ''
})

// 表单验证规则
const rules = {
  contractNo: [
    { required: true, message: '请输入购销合同号', trigger: 'blur' },
    { min: 1, max: 50, message: '购销合同号长度应在1-50个字符之间', trigger: 'blur' }
  ]
}

// 监听弹框显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹框打开时重置表单
    formData.contractNo = ''
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }
})

// 处理弹框显示状态变化
const handleVisibleChange = (visible) => {
  emit('update:visible', visible)
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
}

// 确认操作
const handleConfirm = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    loading.value = true
    
    // 构造请求参数
    const params = {
      id: props.selectedData.id, // 原数据ID
      contractNo: formData.contractNo.trim() // 新购销合同号
    }
    
    // 触发确认事件，传递参数给父组件
    emit('confirm', params)
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件调用
const resetLoading = () => {
  loading.value = false
}

defineExpose({
  resetLoading
})
</script>

<style lang="less" scoped>
.modal-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
