import { baseColumns } from '@/view/common/baseColumns'

const {baseColumnsShow} = baseColumns()

export function getColumns() {
  const commColumns = [
    'id',
    'productName',
    'productModel',
    'specification',
    'weight',
    'supplier',
    'transportMode',
    'port',
    'qty',
    'unit',
    'requestDeliveryDate'
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  // table表格字段设置
  const totalColumns = [
    {
      minWidth: 160,
      title: '商品名称',
      align: 'center',
      dataIndex: 'productName',
      resizable: true,
      key: 'productName'
    },
    {
      minWidth: 120,
      title: '产品型号',
      align: 'center',
      dataIndex: 'productModel',
      resizable: true,
      key: 'productModel'
    },
    {
      width: 100,
      title: '规格',
      align: 'center',
      dataIndex: 'specification',
      resizable: true,
      key: 'specification'
    },
    {
      minWidth: 90,
      title: '克重',
      align: 'center',
      dataIndex: 'weight',
      resizable: true,
      key: 'weight'
    },
    {
      minWidth: 180,
      title: '供应商',
      align: 'center',
      dataIndex: 'supplier',
      resizable: true,
      key: 'supplier',
      withValue: false
    },
    {
      minWidth: 95,
      title: '运输方式',
      align: 'center',
      dataIndex: 'transportMode',
      resizable: true,
      key: 'transportMode',
      withValue: false
    },
    {
      minWidth: 180,
      title: '港口',
      align: 'center',
      dataIndex: 'port',
      resizable: true,
      key: 'port',
      withValue: true
    },
    {
      minWidth: 80,
      title: '数量',
      align: 'center',
      dataIndex: 'qty',
      resizable: true,
      key: 'qty',
      precision: 6
    },
    {
      width: 95,
      title: '单位',
      align: 'center',
      dataIndex: 'unit',
      resizable: true,
      key: 'unit',
      withValue: true
    },
    {
      minWidth: 120,
      title: '要求到货日',
      align: 'center',
      dataIndex: 'requestDeliveryDate',
      resizable: true,
      key: 'requestDeliveryDate'
    }
  ]

  return {
    tableColumns: totalColumns.filter(item => columnsConfig.includes(item.key)),
  }
}
