<template>
  <section class="cs-action cs-action-tab" ref="tabRef">
    <div class="cs-tab">
      <a-tabs class="sticky-header" v-model:activeKey="currentTab" size="small" :tabBarStyle="tabBarStyle">
        <a-tab-pane key="headTab" tab="表头表体">
          <order-notice-edit/>
        </a-tab-pane>

        <a-tab-pane v-if="!edit.commonAddFlag.value" key="attach" tab="归档附件">
          <order-notice-attach/>
        </a-tab-pane>

        <a-tab-pane v-if="!edit.commonAddFlag.value" key="aeo" tab="审批记录">
          <!-- 审批记录 -->
        </a-tab-pane>

        <template #rightExtra>
          <div class="cs-tab-icon" @click="handleBack">
            <global-icon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>

<script setup>
import OrderNoticeEdit from '@/view/auxiliaryMaterials/orderNotice/OrderNoticeEdit.vue'
import OrderNoticeAttach from '@/view/auxiliaryMaterials/orderNotice/components/OrderNoticeAttach.vue'
import { inject, ref, watch, provide, onMounted, onBeforeUnmount } from 'vue'
import { observe, unobserve } from '@/utils/observe'

const edit = inject('edit')

// tab bar样式
const tabBarStyle = {
  background: '#fff',
  position: 'sticky',
  top: '0',
  zIndex: '100',
}

// tabs
const tabs = ref({
  headTab: true,
  attach: false,
  aeo: false
})

// 当前tab
const currentTab = ref('headTab')

// 监听当前tab变化
watch(currentTab, (value) => {
  for (let key in tabs) {
    tabs[key] = false
  }
  tabs[value] = true
})

/**
 * 返回
 */
function handleBack() {
  edit.back(true)
}

// tab ref
const tabRef = ref(null)

// tab 高度
const tabHeight = ref(0)
provide('tabHeight', tabHeight)

onMounted(() => {
  tabHeight.value = tabRef.value.clientHeight
  observe(tabRef.value, config => {
    tabHeight.value = config.height
  })
})

onBeforeUnmount(() => {
  unobserve(tabRef.value)
})

defineOptions({
  name: 'OrderNoticeTabs'
})
</script>
