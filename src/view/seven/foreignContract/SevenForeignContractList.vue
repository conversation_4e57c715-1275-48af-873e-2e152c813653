<template>
  <section class="dc-section" ref="dcSectionRef">
    <div v-show="show" class="cs-action">
      <!-- 查询区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button v-show="showSearch" class="cs-margin-right cs-refresh"
                          size="small" type="primary" @click="handlerRefresh">
                  <template #icon>
                    <global-icon type="redo" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{ localeContent('m.common.button.query') }}
                  <template #icon>
                    <global-icon type="search" style="color:#fff" />
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <global-icon v-show="!showSearch" type="down" style="color:#fff" />
                    <global-icon v-show="showSearch" type="up" style="color:#fff" />
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine" />
          <div ref="area_search">
            <div v-show="showSearch">
              <seven-foreign-contract-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:add']">
          <a-button size="small" @click="handleAdd">
            <template #icon>
              <global-icon type="plus" style="color:green" />
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:update']">
          <a-button size="small" @click="handleEdit">
            <template #icon>
              <global-icon type="form" style="color:orange" />
            </template>
            {{ localeContent('m.common.button.update') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handleDelete">
            <template #icon>
              <global-icon type="delete" style="color:red" />
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:export']">
          <a-button size="small" :loading="exportLoading" @click="handleExport">
            <template #icon>
              <global-icon type="folder-open" style="color:orange" />
            </template>
            {{ localeContent('m.common.button.export') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handleConfirm">
            <template #icon>
              <global-icon type="check" style="color:green" />
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:invalidate']">
          <a-button size="small" :loading="invalidateLoading" @click="handleInvalidate">
            <template #icon>
              <global-icon type="close-square" style="color:red" />
            </template>
            作废
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:sendAudit']">
          <a-button size="small" :loading="sendAuditLoading" @click="handleSendAudit">
            <template #icon>
              <global-icon type="cloud" style="color:deepskyblue" />
            </template>
            发送审核
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:copyVersion']">
          <a-button size="small" :loading="versionCopyLoading" @click="handleVersionCopy">
            <template #icon>
              <global-icon type="snippets" style="color:deepskyblue" />
            </template>
            版本复制
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:seven-foreignContract:printCountersignSheet']">
          <a-dropdown :disabled="printCountersignSheetLoading">
            <template #overlay>
              <a-menu @click="handlePrintCountersignSheet">
                <a-menu-item key="PDF">会签单(PDF)</a-menu-item>
                <a-menu-item key="XLSX">会签单(XLSX)</a-menu-item>
              </a-menu>
            </template>
            <a-button class="button" size="small" :loading="printCountersignSheetLoading">
              <template #icon>
                <GlobalIcon type="cloud-download" style="color:blue" />
              </template>
              打印会签单
            </a-button>
          </a-dropdown>
        </div>
        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <cs-table-col-settings
            :resId="tableKey"
            :tableKey="tableKey + '-sevenForeignContract'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </cs-table-col-settings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="remove-table-border-add-bg head-table"
          size="small"
          bordered
          column-drag
          :custom-row="customRow"
          :pagination="false"
          :columns="showColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :style="listTableStyle"
          :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, record, text}">
            <template v-if="column.dataIndex === 'operation'">
              <div class="operation-container">
                <a-button
                  v-if="record.dataStatus === DATA_STATUS.DRAFT"
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <global-icon type="form" style="color:#e93f41" />
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="record.dataStatus === DATA_STATUS.DRAFT ? operationEdit('view') : undefined"
                >
                  <template #icon>
                    <global-icon type="search" style="color:#1677ff" />
                  </template>
                </a-button>
              </div>
            </template>
            <template v-else-if="['buyer', 'seller'].includes(column.dataIndex)">
              <span>{{ cmbShowRender(text, optionsConfig.merchantOptions) }}</span>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页区域 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- tabs -->
    <seven-foreign-contract-tabs v-if="!show" />
  </section>
</template>

<script setup>
import { GlobalIcon } from '@/components/icon'
import CsTableColSettings from '@/components/settings/CsTableColSettings'
import BreadCrumb from '@/components/breadcrumb/BreadCrumb.vue'
import SevenForeignContractSearch from '@/view/seven/foreignContract/SevenForeignContractSearch'
import SevenForeignContractTabs from '@/view/seven/foreignContract/SevenForeignContractTabs'
import { ref, provide, createVNode, onBeforeUnmount, computed, watchEffect, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { message, Modal } from 'ant-design-vue'
import ycCsApi from '@/api/ycCsApi'
import { useCommon } from '@/view/common/useCommon'
import { useColumnsRender } from '@/view/common/useColumnsRender'
import { localeContent } from '@/view/utils/commonUtil'
import { getHeadColumns } from '@/view/seven/foreignContract/js/columns.jsx'
import { editStatus, DATA_STATUS, EMPTY } from '@/view/common/constant'
import {
  getOptionsData, deleteContract, confirmContract, invalidateContract
  , checkVersionCopy, versionCopy, printSheet
} from '@/api/seven/foreignContract'
import { observe, unobserve, trigger } from '@/utils/observe'
import { APPROVAL, EDIT, OPTIONS_CONFIG } from '@/view/seven/foreignContract/js/handle'

const {
  ajaxUrl,
  showSearch,
  headSearch,
  handleShowSearch,
  handlerSearch,
  handlerRefresh,
  getList,
  show,
  tableLoading,
  dataSourceList,
  gridData,
  editConfig,
  onSelectChange,
  operationEdit,
  page,
  onPageChange,
  exportLoading,
  doExport
} = useCommon()

const { cmbShowRender } = useColumnsRender()

/**
 * 检查选择行
 * @param operation 操作
 * @param multi 是否多选
 * @returns {Promise<unknown>} promise
 */
function checkSelectedRows(operation, multi = true) {
  const keys = gridData.selectedRowKeys
  return new Promise((resolve, reject) => {
    if (keys.length <= 0) {
      reject(`请选择一条要${operation}的数据`)
      return
    }
    if (keys.length > 1 && !multi) {
      reject(`只能选择一条数据${operation}`)
      return
    }
    resolve(multi ? { keys, rows: gridData.selectedData } : {
      key: keys[0],
      row: gridData.selectedData.filter(item => item['id'] === keys[0])[0]
    })
  })
}

// 配置表格列、导出列
const { tableColumns, excelColumns } = getHeadColumns()

// 原始显示列
const originalColumns = ref((() => {
  for (let columns of tableColumns) {
    columns.visible = true
  }
  return tableColumns
})())

// 当前显示列
const showColumns = ref([...originalColumns.value])

// 自定义显示列更改回调
function customColumnChange(settingColumns) {
  showColumns.value = settingColumns.filter(item => item.visible === true)
}

// 表格唯一key
const tableKey = ref(window['$vueApp'] ? window.majesty.router.currentRoute.value.path : useRoute().path)

// 请求url
ajaxUrl.selectAllPage = ycCsApi.seven.foreignContract.head.list
ajaxUrl.exportUrl = ycCsApi.seven.foreignContract.head.export

// 选项配置
const optionsConfig = ref({
  // 客商
  merchantOptions: EMPTY.ARRAY,
  // 制单人
  makerOptions: EMPTY.ARRAY,
  // 商品类别
  productTypeOptions: EMPTY.ARRAY,
  // 价格条款
  priceTermsOptions: EMPTY.ARRAY,
  // 城市
  cityOptions: EMPTY.ARRAY,
  // 币制
  currOptions: EMPTY.ARRAY,
  // 港口
  portOptions: EMPTY.ARRAY,
  // 单位
  unitOptions: EMPTY.ARRAY
})

/**
 * 初始化选项配置
 */
async function initOptionsConfig() {
  const res = await getOptionsData()
  if (!res.success) {
    message.warn('选项配置初始化失败')
    return
  }
  const data = res.data
  optionsConfig.value.merchantOptions = data.merchantOptions
  optionsConfig.value.makerOptions = data.makerOptions
  optionsConfig.value.merchantOptions = data.merchantOptions
  optionsConfig.value.priceTermsOptions = data.priceTermsOptions
  optionsConfig.value.productTypeOptions = data.productTypeOptions
  const cityOptions = data.cityOptions
  cityOptions.forEach(item => {
    item.enName = item.ext
    delete item.ext
  })
  optionsConfig.value.cityOptions = cityOptions
  const currOptions = data.currOptions
  currOptions.forEach(item => {
    item.value = item.ext
    delete item.ext
  })
  optionsConfig.value.currOptions = currOptions
  optionsConfig.value.portOptions = data.portOptions
  optionsConfig.value.unitOptions = data.unitOptions
}

/**
 * 自定义行
 * @param record 数据记录
 * @returns {{onDblclick: *, style: {cursor: string}}} 行配置
 */
function customRow(record) {
  return {
    onDblclick: () => {
      handleRowDblclick(record)
    },
    style: { cursor: 'pointer' }
  }
}

/**
 * 双击行
 * @param record 数据记录
 */
function handleRowDblclick(record) {
  if (DATA_STATUS.DRAFT !== record.dataStatus) {
    message.warn('仅编制状态数据允许编辑')
    return
  }
  handleEditByRow(record)
}

/**
 * 导出
 */
function handleExport() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`出料加工进口薄片-外商合同${timestamp}.xlsx`, ref(excelColumns))
}

/**
 * 新增
 */
function handleAdd() {
  show.value = false
  editConfig.value.editStatus = editStatus.ADD
  editConfig.value.editData = {}
  editConfig.value.headId = ''
}

/**
 * 编辑
 */
function handleEdit() {
  checkSelectedRows('编辑', false).then(selected => {
    const { row } = selected
    if (DATA_STATUS.DRAFT !== row.dataStatus) {
      message.warn('仅编制状态数据支持编辑操作')
      return
    }
    handleEditByRow(selected.row)
  }).catch(error => {
    message.warn(error)
  })
}

// 删除
const deleteLoading = ref(false)

async function handleDelete() {
  const doDelete = async (id) => {
    deleteLoading.value = true
    try {
      const res = await deleteContract(id)
      if (res.success) {
        message.success('删除成功')
        getList()
      }
    } finally {
      deleteLoading.value = false
    }
  }
  try {
    const { key, row } = await checkSelectedRows('删除', false)
    if (row.dataStatus !== DATA_STATUS.DRAFT) {
      message.warn('仅编制状态数据允许删除')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '删除',
      cancelText: '取消',
      content: '确认删除所选项吗？',
      onOk: async () => {
        await doDelete(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 确认
const confirmLoading = ref(false)

async function handleConfirm() {
  const doConfirm = async (id) => {
    confirmLoading.value = true
    try {
      const res = await confirmContract(id)
      if (res.success) {
        message.success('确认成功')
        getList()
      }
    } finally {
      confirmLoading.value = false
    }
  }
  try {
    const { key, row } = await checkSelectedRows('确认', false)
    if (row.dataStatus === DATA_STATUS.CONFIRMED) {
      message.warn('该数据已确认，无需重复操作')
      return
    }
    if (row.dataStatus === DATA_STATUS.INVALID) {
      message.warn('该数据已作废，不允许确认')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '是否确认所选项？',
      onOk: async () => {
        await doConfirm(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 作废
const invalidateLoading = ref(false)

async function handleInvalidate() {
  const doInvalidate = async (id) => {
    invalidateLoading.value = true
    try {
      const res = await invalidateContract(id)
      if (res.success) {
        message.success('作废成功')
        getList()
      }
    } finally {
      invalidateLoading.value = false
    }
  }
  try {
    const { key, row } = await checkSelectedRows('作废', false)
    if (row.dataStatus === DATA_STATUS.INVALID) {
      message.warn('该数据已作废，无需重复操作')
      return
    }
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '作废',
      cancelText: '取消',
      content: '是否作废所选项？',
      onOk: async () => {
        await doInvalidate(key)
      }
    })
  } catch (reason) {
    message.warn(reason)
  }
}

// 版本复制
const versionCopyLoading = ref(false)

function handleVersionCopy() {
  const doVersionCopy = async (row) => {
    versionCopyLoading.value = true
    try {
      const { data } = await checkVersionCopy(row.contractNo)
      if ('1' === data) {
        Modal.confirm({
          title: '提醒',
          icon: createVNode(ExclamationCircleOutlined),
          okText: '确认',
          cancelText: '取消',
          content: '当前单据存在有效数据，是否将其作废并重新生成一份新数据？',
          onOk: async () => {
            await actionVersionCopy(row)
          }
        })
      } else {
        await actionVersionCopy(row)
      }
    } finally {
      versionCopyLoading.value = false
    }
  }
  const actionVersionCopy = async (row) => {
    const res = await versionCopy(row)
    if (res.success) {
      message.success('版本复制成功')
      getList()
    } else {
      message.error(res.message)
    }
  }
  checkSelectedRows('版本复制', false).then(selected => {
    const { row } = selected
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '版本复制',
      cancelText: '取消',
      content: '是否版本复制所选项？',
      onOk: async () => {
        await doVersionCopy(row)
      }
    })
  }).catch(error => {
    message.warn(error)
  })
}

// 发送审核
const sendAuditLoading = ref(false)

function handleSendAudit() {
  checkSelectedRows('发送审核', false).then(async selected => {
    sendAuditLoading.value = true
    try {
      const { key } = selected
      const params = {
        sid: key,
        businessType: '7',
        billType: 'contract',
        ids: [key]
      }
      const res = await window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.sendAudit, params)
      if (res['code'] === 200) {
        message.success('发送审核成功')
        getList()
      } else {
        message.error(res['message'])
      }
    } finally {
      sendAuditLoading.value = false
    }
  }).catch(error => {
    message.warn(error)
  })
}

// 打印会签单
const printCountersignSheetLoading = ref(false)

function handlePrintCountersignSheet(value) {
  checkSelectedRows('打印会签单', true).then(async selected => {
    printCountersignSheetLoading.value = true
    try {
      const { keys } = selected
      await printSheet(value.key, keys)
      message.success('打印会签单成功')
      gridData.selectedData = []
      gridData.selectedRowKeys = []
    } finally {
      printCountersignSheetLoading.value = false
    }
  }).catch(error => {
    message.warn(error)
  })
}

/**
 * 行内编辑
 * @param record 数据记录
 */
function handleEditByRow(record) {
  show.value = false
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = record
  editConfig.value.headId = record.id
}

/**
 * 行内查看
 * @param record 数据记录
 */
function handleViewByRow(record) {
  show.value = false
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.editData = record
  editConfig.value.headId = record.id
}

/**
 * 返回
 * @param requireFlush 是否需要刷新
 */
function handleBack(requireFlush = true) {
  show.value = true
  editConfig.value.editStatus = editStatus.SHOW
  editConfig.value.headId = ''
  editConfig.value.editData = {}
  if (requireFlush) {
    getList()
  }
  trigger(dcSectionRef.value)
}

// 列表表格样式
const listTableStyle = ref({
  minHeight: '300px'
})

// section高度
const dcSectionHeight = ref(0)
const dcSectionRef = ref(null)

watchEffect(() => {
  const currentHeight = dcSectionHeight.value - 115 - (showSearch.value ? 80 : 0)
  const tableMinHeight = Math.max(currentHeight, 300)
  listTableStyle.value.minHeight = tableMinHeight + 'px'
  if (dcSectionRef.value) {
    dcSectionRef.value.style.setProperty('--table-body-min-height', (tableMinHeight - 45) + 'px')
  }
})

// 编辑数据
provide(EDIT, {
  config: editConfig,
  back: handleBack,
  toEdit: (data) => {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData = data
    editConfig.value.headId = data.id
  },
  toShow: () => {
    if (!(editConfig.value.editStatus === editStatus.SHOW)) {
      editConfig.value.editStatus = editStatus.SHOW
    }
  },
  commonAddFlag: computed(() => editConfig.value.editStatus === editStatus.ADD),
  commonShowFlag: computed(() => editConfig.value.editStatus === editStatus.SHOW)
})

// 选项配置
provide(OPTIONS_CONFIG, optionsConfig)

// 审核信息
const approval = ref({
  flag: false
})
provide(APPROVAL, approval)

onMounted(async () => {
  observe(dcSectionRef.value, config => {
    dcSectionHeight.value = config.height
  })
  await initOptionsConfig()
  getList()
})

onBeforeUnmount(() => {
  unobserve(dcSectionRef.value)
})

defineOptions({
  name: 'SevenForeignContractList'
})
</script>
<style scoped lang="less">
.head-table :deep(.surely-table-body) {
  min-height: var(--table-body-min-height);
}
</style>
