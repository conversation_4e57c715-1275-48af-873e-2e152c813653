<template>
  <section class="cs-action cs-action-tab" ref="tabRef">
    <div class="cs-tab">
      <a-tabs class="sticky-header" v-model:activeKey="currentTab" size="small" :tabBarStyle="tabBarStyle">
        <a-tab-pane key="editTab" tab="表头表体">
          <seven-foreign-contract-edit v-if="!approval.flag" />
          <seven-foreign-contract-audit-edit v-else />
        </a-tab-pane>

        <a-tab-pane v-if="!addFlag" key="attachTab" tab="归档附件">
          <seven-foreign-contract-attach />
        </a-tab-pane>

        <a-tab-pane v-if="!addFlag" key="aeoTab" tab="审批记录">
          <cs-aeo-info-list :sid="edit.config.value['headId']" />
        </a-tab-pane>

        <template #rightExtra>
          <div class="cs-tab-icon" @click="handleBack">
            <global-icon type="close-circle" style="color:#000" />
          </div>
        </template>
      </a-tabs>
    </div>
  </section>
</template>

<script setup>
import CsAeoInfoList from '@/components/aeo/CsAeoInfoList.vue'
import SevenForeignContractEdit from '@/view/seven/foreignContract/compoents/SevenForeignContractEdit.vue'
import SevenForeignContractAttach from '@/view/seven/foreignContract/compoents/SevenForeignContractAttach.vue'
import SevenForeignContractAuditEdit from '@/view/seven/foreignContract/audit/SevenForeignContractAuditEdit.vue'
import { inject, ref, watch, provide, onMounted, onBeforeUnmount } from 'vue'
import { observe, unobserve } from '@/utils/observe'
import { EDIT, TABS_HEIGHT, APPROVAL } from '@/view/seven/foreignContract/js/handle'

const edit = inject(EDIT)

const approval = inject(APPROVAL)

const addFlag = edit.commonAddFlag

// tab bar样式
const tabBarStyle = {
  background: '#fff',
  position: 'sticky',
  top: '0',
  zIndex: '100'
}

// tabs
const tabs = ref({
  editTab: true,
  attachTab: false,
  aeoTab: false
})

// 当前tab
const currentTab = ref('editTab')

// 监听当前tab变化
watch(currentTab, (value) => {
  for (let key in tabs) {
    tabs[key] = false
  }
  tabs[value] = true
})

/**
 * 返回
 */
function handleBack() {
  edit.back(true)
}

const tabRef = ref(null)

const tabHeight = ref(0)

provide(TABS_HEIGHT, tabHeight)

onMounted(() => {
  tabHeight.value = tabRef.value.clientHeight
  observe(tabRef.value, config => {
    tabHeight.value = config.height
  })
})

onBeforeUnmount(() => {
  unobserve(tabRef.value)
})

defineOptions({
  name: 'SevenForeignContractTabs'
})
</script>
