import HelloWorld from '@/view/helloWorld'
import Base from '@/view/base'
import DecErpI from '@/view/dec/imported_cigarettes'
import CostManage from '@/view/costManage'
import Params from '@/view/params'
import Payment from '@/view/payment'
import importedCigarettes from '../view/importedCigarettes'
import audit from '../view/audit'
import BaseInfoCustomerParams from '@/view/baseInfoCustomerParams'
import Quo from "@/view/quo";
import incoming from "@/view/dec/incoming";
import smokeMachine from "@/view/dec/smoke_machine";
import auxiliaryMaterials from '@/view/auxiliaryMaterials'
import nonAuxiliaryMaterials from '@/view/nonAuxiliaryMaterials'
import warehouse from "@/view/warehouse";
import equipment from "@/view/equipment"
import deliveryOrder from "@/view/deliveryOrder"
import iEBusiness from "@/view/iEBusiness";
import Bp from "@/view/dec/bp";
import purchaseOrder from "@/view/purchaseOrder"
import seven from '@/view/seven'
import ExportGoods from '@/view/dec/export'
import annualPlan from '@/view/annualPlan'

export default [
  ...HelloWorld,
  ...Base,
  ...BaseInfoCustomerParams,
  ...CostManage,
  ...Params,
  ...Payment,
  ...DecErpI,
  ...importedCigarettes,
  ...Quo,
  ...incoming,
  ...auxiliaryMaterials,
  ...nonAuxiliaryMaterials,
  ...warehouse,
  ...audit,
  ...equipment,
  ...smokeMachine,
  ...deliveryOrder,
  ...iEBusiness,
  ...Bp,
  ...purchaseOrder,
  ...seven,
  ...ExportGoods,
  ...annualPlan
]
