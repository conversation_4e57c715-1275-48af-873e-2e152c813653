// import * as Vue from 'vue'
import axios from 'axios'
import {message} from "ant-design-vue";
import MessageUtil from "@/view/utils/MessageUtil";
// import store from '@/store'
// import router from '@/router'
// import { VueAxios } from './axios'
// import MessageUtil from './MessageUtil'
// import { ACCESS_TOKEN, MENU_MAP } from "@/store/mutation-types"
// import LocaleUtil from "./LocaleUtil";

/**
 * 【指定 axios的 baseURL】
 * 如果手工指定 baseURL: '/yuncheng-boot'
 * 则映射后端域名，通过 vue.config.js
 * @type {*|string}
 */
// let apiBaseUrl = bootConfig[process.env.NODE_ENV].VUE_APP_API;
let headers ={
  'X-Access-Token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTUwMjM2NjYsInVzZXJuYW1lIjoieWMwMDEifQ.E_8VnChC_J5gTsIKY6yyAyMKseZ-JzvMhBIaJs1jV9E'
}
// 创建 axios 实例
const service = axios.create({
  //baseURL: '/yuncheng-boot',
  baseURL: '', // api base_url
  timeout: 90000 ,// 请求超时时间
  headers
})

const alertWithFilter = function(error, func) {
  if (error
    && error.response
    && error.config
    && error.response.url
    && error.response.config.url.startsWith("/dd/track")) {
    // skip alert message
  } else {
    func()
  }
}

const err = (error) => {
  console.log('err', error)
  if (error.response) {
    let data = error.response.data
    // const token = Vue.ls.get(ACCESS_TOKEN)
    switch (error.response.status) {
      case 403:
        alertWithFilter(error, () => {
          // MessageUtil.error(LocaleUtil.getLocale('m.common.tip.access_denied'), LocaleUtil.getLocale('m.common.tip.system_prompt'))
        })
        break
      case 500:
        break
      case 404:
        alertWithFilter(error, () => {
          // MessageUtil.error(LocaleUtil.getLocale('m.common.tip.resource_not_found'),LocaleUtil.getLocale('m.common.tip.system_prompt'))
        })
        break
      case 504:
        alertWithFilter(error, () => {
          // MessageUtil.error(LocaleUtil.getLocale('m.common.tip.network_timeout'), LocaleUtil.getLocale('m.common.tip.system_prompt'))
        })
        break
      case 401:
        if(data.indexOf("token.is.multiple") > -1){
          alertWithFilter(error, () => {
            // MessageUtil.errorOne(401,LocaleUtil.getLocale('m.common.tip.multiple_login'),LocaleUtil.getLocale('m.common.tip.system_prompt'))
          })
        }else {
          alertWithFilter(error, () => {
            // MessageUtil.error(LocaleUtil.getLocale('m.common.tip.unauthorized'),LocaleUtil.getLocale('m.common.tip.system_prompt'))
          })
        }
        // if (token) {
        //   store.dispatch('Logout').then(() => {
        //     setTimeout(() => {
        //       window.location.reload()
        //     }, 1500)
        //   })
        // }
        break
      default:
        alertWithFilter(error, () => {
          // MessageUtil.error(data.message, LocaleUtil.getLocale('m.common.tip.system_prompt'))
        })
        break
    }
  }
  return Promise.reject(error)
};



// response interceptor
service.interceptors.response.use((response) => {
  // 如果代码不是200，则返回错误
  // if (response.data.code === 400) {
  //   // MessageUtil.error(response.data.message, LocaleUtil.getLocale('m.common.tip.system_prompt'))
  //   message.error(response.data.message)
  // }
  if(response.config.includeHeader){
    return response
  }
  return response.data
}, err)

const installer = {
  vm: {},
  install (Vue, router = {}) {
    Vue.use(VueAxios, router, service)
  }
}

export {
  installer as VueAxios,
  service as axios
}

