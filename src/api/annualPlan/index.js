import ycCsApi from '@/api/ycCsApi'

export const getOptionsData = () =>
  window.majesty.httpUtil.getAction(ycCsApi.annualPlan.options)

export const addAnnualPlan = params =>
  window.majesty.httpUtil.postAction(ycCsApi.annualPlan.add, params)

export const modifyAnnualPlan = (id, params) =>
  window.majesty.httpUtil.putAction(`${ycCsApi.annualPlan.modify}/${id}`, params)

export const removeAnnualPlan = ids =>
  window.majesty.httpUtil.deleteAction(`${ycCsApi.annualPlan.remove}/${ids}`)
