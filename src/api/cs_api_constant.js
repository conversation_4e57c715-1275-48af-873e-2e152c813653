import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";


//
// // 客户基础信息列表
// export const insertClient = (params) =>window.majesty.httpUtil.postAction(ycCsApi.biClientInfo.insert, params)
// export const updateClient = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.biClientInfo.update}/${sid}`, params)
// export const deleteClient = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.biClientInfo.update}/${sids}`)
//
//
//
// /* shipfrom 方法 */
//
// export const insertShipfrom = (params) => window.majesty.httpUtil.postAction(ycCsApi.biShipfrom.insert, params)
// export const updateShipfrom = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.biShipfrom.update}/${sid}`, params)
// export const deleteShipfrom = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.biShipfrom.update}/${sids}`)


/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params)=>window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) =>window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
/* 根据进货信息表体的sid 获取进货信息  */
export const getNonInComingListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.bizNonInComingList.getIncomingGoodsListBySid}/${sid}`,params)
/* 非国营辅料 进货管理 - 表头 */
export const insertNonInComingHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingHead.insert, params)
export const updateNonInComingHead = (id,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizNonInComingHead.update}/${id}`, params)
/* 非国营辅料 进货管理 - 表体 */
export const insertNonInComingList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizNonInComingList.insert, params)
export const updateNonInComingList = (params) => window.majesty.httpUtil.putAction(ycCsApi.bizNonInComingList.update, params)

/* 进货信息 */
export const insertInComingHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizInComingHead.insert, params)
export const updateInComingHead = (id,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizInComingHead.update}/${id}`, params)
/* 进货信息表体 */
export const insertInComingList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizInComingList.insert, params)
export const updateInComingList = (params) => window.majesty.httpUtil.putAction(ycCsApi.bizInComingList.update, params)
/* 根据进货信息表体的sid 获取进货信息  */
export const getInComingListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.bizInComingList.getIncomingGoodsListBySid}/${sid}`,params)
/* 证件信息 */
export const insertInComingDocument = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIDocument.insert, params)
export const updateInComingDocument = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIDocument.update, params)









/* 判断订单表头是否流向下一级数据 */
export const checkOrderHeadIsNextModule = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.checkOrderHeadIsNextModule, params)
/* 进口订单表头操作  */
export const insertBizIOrderHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.insert, params)
export const updateBizErpIOrderHead = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIOrderHead.update}/${sid}`, params)
export const deleteBizErpIOrderHead = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizIOrderHead.update}/${sids}`)
export const printBizIOrderHead = (sid,sType) => window.majesty.httpUtil.postAction(`${ycCsApi.bizIOrderHead.print}/${sid}/${sType}`)
/* 重启订单号-选择弹框 */
export const rebootOrderNo = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.rebootOrderNo, params)
/* 进口订单表头确认 */
export const confirmIOrderHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.confirmIOrderHead, params)
/* 获取进口合同信息 */
export const getIContractList = (params,page) => window.majesty.httpUtil.postAction(`${ycCsApi.bizIOrderHead.getIContractList}?page=${page.current}&limit=${page.pageSize}`, params)
/* 提取进口合同信息 =>  进口订单信息 */
export const generateIOrder = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.generateIOrder, params)
/* 获取订单表头金额汇总 */
export const getOrderHeadTotal = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.getOrderHeadTotal, params)

/* 获取当前企业的供应商列表信息 */
export const getOrderSupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.getOrderSupplierList, params)

/* 版本复制 */
export const copyOrderVersion = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.copyVersion, params)
/* 版本复制校验 */
export const checkOrderNoNotCancel = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.checkOrderNoNotCancel, params)
/* 判断进口订单 下游 =>  进口费用是否存在有效数据 */
export const checkNextModuleExistEffectiveData =  (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.checkNextModuleExistEffectiveData, params)

/* 作废数据 */
export const orderCancelData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderHead.cancelData, params)


/*销售*/
export const getISellHeadMessage = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellHead.selectByheadId, params)
export const getISellHeadUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizISellHead.update}/${sid}`, params)
export const getISellList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellList.selectList, params)
export const getISellListUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizISellList.update}/${sid}`, params)
export const getISellChargeback = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.bizISellHead.chargeback}/${sid}`)
export const getISellchargebackIncoming = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.bizISellHead.chargebackIncoming}/${sid}`)
export const updateSellList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellList.updateSellList, params)
export const confirmOrderHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellHead.confirm, params)
export const confirmIncoming = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellHead.confirmIncoming, params)
export const confirmRefreshList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellHead.confirmRefreshList, params)
export const getSellInvoiceNoSumData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellList.getSumDataByInvoice, params)
export const getSumDataByInvoiceSummary = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellList.getSumDataByInvoiceSummary, params)


/*入库*/
export const getIWarehouseReceiptHeadMessage = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptHead.selectByheadId, params)
export const getIWarehouseReceiptHeadUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIWarehouseReceiptHead.update}/${sid}`, params)
export const getIWarehouseReceiptList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptList.selectList, params)
export const getIWarehouseReceiptListUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIWarehouseReceiptList.update}/${sid}`, params)
export const getIWarehouseReceiptListSumData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptList.getSumData, params)
export const getIWarehouseReceiptAbandonedData = (status) => window.majesty.httpUtil.postAction(`${ycCsApi.bizIWarehouseReceiptHead.abandonedData}/${status}`)
export const generateBizIWarehouseReceipt = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIWarehouseReceiptHead.generateBizIWarehouseReceipt, params)
export const getWarehouseReceiptListBySid = (sid,params) => window.majesty.httpUtil.postAction(`${ycCsApi.bizIWarehouseReceiptList.getWarehouseReceiptListBySid}/${sid}`,params)




/*出库*/
export const getIReceiptHeadMessage = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIReceiptHead.selectByheadId, params)
export const getIReceiptHeadUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIReceiptHead.update}/${sid}`, params)
export const getIReceiptList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIReceiptList.selectList, params)
export const getIReceiptListUpdate = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIReceiptList.update}/${sid}`, params)
export const getIReceiptExport = (sid,params) => window.majesty.httpUtil.getAction(`${ycCsApi.bizIReceiptHead.export}/${sid}`,params)
export const getSumDataByInvoiceSummaryList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIReceiptList.getSumDataByInvoiceSummary, params)
export const confirmReceiptHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizISellHead.confirmReceipt, params)

/* 进口订单表体操作 */
export const insertBizErpIOrderList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderList.insert, params)
export const updateBizErpIOrderList = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIOrderList.update}/${sid}`, params)
export const deleteBizErpIOrderList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizIOrderList.delete}/${sids}`)
/* 获取进口订单表体汇总数据 */
export const getITotal = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIOrderList.getITotal, params)


/* 根据订单sid获取进口信息表体 */
export const getOrderListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.bizIOrderList.getOrderListBySid}/${sid}`,params)


/* 进口进货信息表头操作  */
export const insertBizIPurchaseHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseHead.insert, params)
export const updateBizIPurchaseHead = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIPurchaseHead.update}/${sid}`, params)
export const updateEntryInfo = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIPurchaseHead.updateEntry}/${sid}`, params)

export const deleteBizIPurchaseHead = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizIPurchaseHead.update}/${sids}`)
/* 获取进货信息表头数据 根据订单表头sid */
export const getPurchaseHeadByOrderSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseHead.getPurchaseHeadByOrderSid, params)



/* 进口进货信息表体操作 */
/* 根据进货信息表体的sid 获取进货信息  */
export const getPurchaseListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.bizIPurchaseList.getPurchaseListBySid}/${sid}`,params)


export const insertBizIPurchaseList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.insert, params)
export const updateBizIPurchaseList = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.bizIPurchaseList.update}/${sid}`, params)
export const deleteBizIPurchaseList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizIPurchaseList.update}/${sids}`)

/* 删除装箱子表数据 */
export const deletePurchaseList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.deletePurchaseList, params)

/* 进口单证操作 */
export const insertBizIDocument = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIDocument.insert, params)
export const getBizIDocument = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIDocument.list, params)
export const innerUpdatePurchaseList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.innerUpdatePurchaseList, params)
export const innerDecTotalUpdatePurchaseList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.innerDecTotalUpdatePurchaseList, params)
export const innerUpdatePurchaseListInvoiceNo = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.innerUpdatePurchaseListInvoiceNo, params)
export const getBizIPurchaseListSumData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.getSumData, params)
/* 回去进口发票号汇总数据 */
export const getPurchaseInvoiceNoSumData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.getSumDataByInvoice, params)





/* 新增装箱子表数据 */
export const addPushListBox = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseListBox.addPushListBox, params)
export const addPushListInvoice = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseList.addPushListInvoice, params)
export const getBizIPurchaseListBoxSumData = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseListBox.getSumData, params)
export const batchUpdateBoxList = (params) => window.majesty.httpUtil.postAction(ycCsApi.bizIPurchaseListBox.batchUpdateBoxList, params)





/* 上传附件信息 */
export const uploadAttachFile = (params) => window.majesty.httpUtil.postAction(ycCsApi.baseAttach.insert, params)

/* 根据headId获取对应的附件列表 */
export const getAttachFileList = (headId) => window.majesty.httpUtil.postAction(`${ycCsApi.baseAttach.getAttechedList}/${headId}`)
export const getAttachFileListByType = (bType,headId) => window.majesty.httpUtil.postAction(`${ycCsApi.baseAttach.getAttechedListByType}/${bType}/${headId}`)

/* 下载文件 */
export const downloadAttachFile = (sid) => window.majesty.httpUtil.getAction(`${ycCsApi.baseAttach.download}/${sid}` )

/* 删除附件信息 */
export const deleteAttachFile = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.baseAttach.delete}/${sids}`)


export const insertQuoList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.quoUrl.bizQuotation.insert, params)
export const updateQuoList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.quoUrl.bizQuotation.update}/${sid}`, params)
export const deleteQuoList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.quoUrl.bizQuotation.delete}/${sids}`)
export const cancelQuoList = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.quoUrl.bizQuotation.cancel}/${sids}`)
export const enableQuoList = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.quoUrl.bizQuotation.enable}/${sids}`)
export const getGName = (commonMark) => window.majesty.httpUtil.postAction(`${ycCsApi.quoUrl.bizQuotation.getGNameList}/${commonMark}`)
export const getQuoSupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.quoUrl.bizQuotation.getSupplierList, params)

export const getStoreIHeadSupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreIHead.getOrderSupplierList, params)
export const insertStoreIHeadList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreIHead.insert, params)
export const updateStoreIHeadList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.warehouseUrl.bizStoreIHead.update}/${sid}`, params)
export const deleteStoreIHeadList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.warehouseUrl.bizStoreIHead.delete}/${sids}`)
export const cancelStoreIHeadList = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.cancel}/${sids}`)
export const enableStoreIHeadList = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.enable}/${sids}`)
export const confirmStoreIHead = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.confirm}`, params)
export const redFlushStoreIHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.redFlush}/${sid}`)
export const checkIsNextModule = (params) => window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreIHead.checkIsNextModule, params)
export const generateIStore = (params) => window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreIHead.generateIOrder, params)
export const sendAuditStoreIHead = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.sendApproval}/${sids}`)
export const listIncomingGoods = (params,page) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.listIncomingGoods}?page=${page.current}&limit=${page.pageSize}`, params)

export const insertStoreIListList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreIList.insert, params)
export const updateStoreIListList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.warehouseUrl.bizStoreIList.update}/${sid}`, params)
export const deleteStoreIListList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.warehouseUrl.bizStoreIList.delete}/${sids}`)
export const getStoreIListListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIList.getListBySid}/${sid}`,params)

export const insertStoreEHeadList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreEHead.insert, params)
export const updateStoreEHeadList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.warehouseUrl.bizStoreEHead.update}/${sid}`, params)
export const deleteStoreEHeadList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.warehouseUrl.bizStoreEHead.delete}/${sids}`)
export const getStoreEHeadListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEHead.getListBySid}/${sid}`,params)
export const getStoreEHeadByHeadSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreEHead.getStoreEHeadByHeadSid, params)
export const confirmStoreEHead = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEHead.confirm}`,params)
export const redFlushStoreEHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEHead.redFlush}/${sid}`)
export const backStoreEHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEHead.back}/${sid}`)
export const backStoreIHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreIHead.back}/${sid}`)

export const insertStoreEListList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.warehouseUrl.bizStoreEList.insert, params)
export const updateStoreEListList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.warehouseUrl.bizStoreEList.update}/${sid}`, params)
export const deleteStoreEListList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.warehouseUrl.bizStoreEList.delete}/${sids}`)
export const getStoreEListListBySid = (sid,params) =>  window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEList.getListBySid}/${sid}`,params)

export const getCustomerAccountSupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.customerAccount.getSupplierList, params)
export const getCustomerAccountSummarySupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.bizCustomerAccountSummary.getSupplierList, params)
export const getCustomerAccountTobacooSupplierList = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.bizCustomerAccountTobacoo.getSupplierList, params)
export const getCreateUserListCustomerAccount = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.customerAccount.getCreateByList, params)
export const getCreateUserListS = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.bizCustomerAccountSummary.getCreateByList, params)
export const getCreateUserListSlice = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.bizCustomerAccountSlice.getCreateByList, params)
export const getCreateUserListT = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.bizCustomerAccountTobacoo.getCreateByList, params)
export const getCreateUserListP = (params) => window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderHead.getCreateByList, params)

export const getDeliveryOrderShipmentsByHeadSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderShipments.getDeliveryOrderShipmentsByHeadSid, params)
export const getDeliveryOrderCertByHeadSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderCert.getDeliveryOrderCertByHeadSid, params)
export const getPurchaseOrderCertByHeadSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderCert.getPurchaseOrderCertByHeadSid, params)
export const getDeliveryOrderInsuranceInfoByHeadSid = (params) => window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.getDeliveryOrderInsuranceInfoByHeadSid, params)
export const getExchangeRateByCurr = (curr) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderInsuranceInfo.getExchangeRate}/${curr}`)
export const updateOrderListList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderList.update}/${sid}`, params)
export const updatePurchaseOrderListList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderList.update}/${sid}`, params)
export const updateOrderCListList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.update}/${sid}`, params)
export const getDeliveryOrderListBySid = (sid,params) =>window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderList.getDeliveryOrderListBySid}/${sid}`, params)
export const getDeliveryOrderCListBySid = (sid,params) =>window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderContainerList.getDeliveryOrderCListBySid}/${sid}`, params)
export const getDeliveryOrderHeadCreateUserList = (params) => window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderHead.getCreateByList, params)
export const getDeliveryOrderHeadCustomerList = (params) => window.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderHead.getCustomerList, params)
export const confirmDeliveryOrderHead = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.confirm}`, params)
export const confirmPurchaseOrderHead = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.confirm}`, params)
export const getDeliveryOrderSid = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.getDeliveryOrderSid}`, params)
export const getPurchaseOrderSid = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.getPurchaseOrderSid}`, params)
export const invalidatePurchaseOrder = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.invalidate}/${sid}`)
export const invalidateDeliveryOrderHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.invalidate}/${sid}`)
export const backDeliveryOrderHead = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.back}/${sid}`)
export const generateDeliveryOrderHead = (params) =>ewindow.majesty.httpUtil.postAction(ycCsApi.deliveryOrder.bizDeliveryOrderHead.insertByContract, params)
export const generatePurchaseOrderHead = (params) => window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderHead.insertByContract, params)
export const generatePurchaseOrderList = (params) => window.majesty.httpUtil.postAction(ycCsApi.purchaseOrder.bizPurchaseOrderHead.insertListByContract, params)
export const listInDeliveryOrderHead = (params,page) => window.majesty.httpUtil.postAction(`${ycCsApi.deliveryOrder.bizDeliveryOrderHead.listInDeliveryOrderHead}?page=${page.current}&limit=${page.pageSize}`, params)
export const listInPurchaseOrderHead = (params,page) => window.majesty.httpUtil.postAction(`${ycCsApi.purchaseOrder.bizPurchaseOrderHead.listInPurchaseOrderHead}?page=${page.current}&limit=${page.pageSize}`, params)
